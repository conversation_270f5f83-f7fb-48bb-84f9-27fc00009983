# LSTM Gold Trading Strategy - Comprehensive Documentation

## Table of Contents
1. [Overview](#overview)
2. [Technical Indicators](#technical-indicators)
3. [Data Pipeline](#data-pipeline)
4. [LSTM Architecture](#lstm-architecture)
5. [Risk Management](#risk-management)
6. [Configuration Management](#configuration-management)
7. [Usage Examples](#usage-examples)
8. [Production Deployment](#production-deployment)

## Overview

This system implements an advanced LSTM-based gold trading strategy using the "Intraday LSTM + Technical-Fusion" approach. The system processes XAUUSD 5-minute data with sophisticated technical indicators to generate buy/sell/hold signals.

### Key Features
- **Asset**: XAUUSD (Gold/USD) - Symbol: "XAUUSD!" in MT5
- **Timeframe**: 5-minute bars for intraday trading
- **Trading Hours**: 09:30-17:00 NY time
- **Update Frequency**: 30-60 second intervals
- **Target Performance**: 56%+ accuracy, 1.3+ Sharpe ratio

## Technical Indicators

### 1. MACD Histogram Slope
**Purpose**: Captures momentum acceleration/deceleration
**Formula**: 
```
MACD = EMA(12) - EMA(26)
Signal = EMA(MACD, 9)
Histogram = MACD - Signal
Slope = LinearRegression(Histogram, 5_periods)
```
**Normalization**: MinMaxScaler to [-1, 1]
**Interpretation**: 
- Positive slope: Increasing bullish momentum
- Negative slope: Increasing bearish momentum

### 2. Stochastic %D (14-period)
**Purpose**: Identifies overbought/oversold conditions
**Formula**:
```
%K = 100 * (Close - Lowest_Low_14) / (Highest_High_14 - Lowest_Low_14)
%D = SMA(%K, 3)
```
**Normalization**: Already 0-100, scaled to [0, 1]
**Interpretation**:
- > 0.8: Overbought (potential sell signal)
- < 0.2: Oversold (potential buy signal)

### 3. Detrended Price Oscillator (DPO)
**Purpose**: Removes trend to focus on cycles
**Formula**:
```
DPO = Close - SMA(Close, 10)[5_periods_ago]
```
**Normalization**: MinMaxScaler to [-1, 1]
**Interpretation**:
- Positive: Price above trend cycle
- Negative: Price below trend cycle

### 4. Bias Ratio (20-period)
**Purpose**: Measures price deviation from moving average
**Formula**:
```
Bias_Ratio = (Close - SMA(Close, 20)) / SMA(Close, 20)
```
**Normalization**: MinMaxScaler to [-1, 1]
**Interpretation**:
- > 0: Price above average (potential resistance)
- < 0: Price below average (potential support)

### 5. Log Returns
**Purpose**: Captures price momentum and volatility
**Formula**:
```
Log_Returns = ln(Close_t / Close_t-1)
```
**Normalization**: StandardScaler with outlier clipping at ±3σ
**Interpretation**:
- Positive: Upward price movement
- Negative: Downward price movement

## Data Pipeline

### Stage 1: Data Preparation
1. **Source**: Kaggle XAUUSD 5-minute historical data + MT5 live updates
2. **Filtering**: 
   - Trading hours: 09:30-17:00 NY time
   - Remove weekends and holidays
   - Handle market gaps (max 72 hours)
3. **Quality Control**:
   - OHLC relationship validation
   - Volume threshold filtering
   - Duplicate removal
   - Missing data interpolation

### Stage 2: Technical Indicator Calculation
1. **Parallel Processing**: All 5 indicators calculated simultaneously
2. **Validation**: Range checks and outlier detection
3. **Normalization**: Feature-specific scaling methods
4. **Quality Metrics**: Completeness and distribution analysis

### Stage 3: LSTM Sequence Generation
1. **Window Size**: 48 timesteps (4 hours of 5-minute bars)
2. **Features**: 5 normalized technical indicators per timestep
3. **Gap Handling**: Skip sequences spanning market gaps
4. **Memory Optimization**: Batch processing with garbage collection

### Stage 4: Target Label Generation (Enhanced)
1. **Forward Analysis**: 8-bar (40-minute) time horizon
2. **Risk Parameters**: 25 pips TP, 12 pips SL (2:1 R:R)
3. **Confidence Filtering**:
   - Buy signals: 30% confidence threshold
   - Sell signals: 60% confidence threshold
   - Hold threshold: 40%
4. **Balance Control**: Achieves ~18% buy, ~64% sell, ~18% hold

## LSTM Architecture

### Model Structure
```python
Sequential([
    LSTM(64, return_sequences=True, input_shape=(48, 5)),
    Dropout(0.2),
    LSTM(32, return_sequences=False),
    Dropout(0.2),
    Dense(16, activation='relu'),
    Dense(3, activation='softmax')  # hold, buy, sell
])
```

### Training Configuration
- **Optimizer**: Adam (lr=0.001)
- **Loss**: Categorical crossentropy
- **Batch Size**: 32
- **Epochs**: 100 (with early stopping)
- **Validation**: Temporal split (70/15/15)
- **Callbacks**: EarlyStopping, ModelCheckpoint

### Performance Targets
- **Minimum Accuracy**: 56%
- **Target Accuracy**: 65%
- **Minimum Precision**: 55%
- **Target Sharpe Ratio**: 1.3

## Risk Management

### Position Sizing
- **Risk per Trade**: 0.25% of account balance
- **Maximum Positions**: 3 concurrent trades
- **Maximum Daily Risk**: 2% of account
- **Maximum Drawdown**: 15%

### Trade Management
- **Take Profit**: 25 pips
- **Stop Loss**: 12 pips
- **Risk:Reward Ratio**: 2:1
- **Break Even**: Move SL to BE at +15 pips
- **Partial Close**: 50% at +20 pips

### Filters and Controls
- **Minimum Confidence**: 60% model prediction
- **Maximum Spread**: 3 pips
- **News Avoidance**: 30 minutes before/after major news
- **Session Filtering**: London and New York sessions only

## Configuration Management

### Master Configuration System
The `master_config.py` provides centralized parameter management:

```python
from master_config import get_config

# Load configuration for specific environment
config = get_config("production")  # or "development", "backtesting"

# Access nested parameters
take_profit = config.get('target_labels.take_profit_pips')
model_params = config.get_section('lstm_model')

# Modify parameters
config.set('risk_management.position_sizing.risk_per_trade', 0.002)

# Save configuration
config.save_config('my_config.json')
```

### Environment-Specific Settings
- **Development**: Faster training, debug logging, lenient thresholds
- **Production**: Conservative risk, warning-level logging, strict validation
- **Backtesting**: Extended data range, performance tracking, detailed reports

## Usage Examples

### 1. Complete Pipeline Execution
```python
# Run complete data preparation and training pipeline
python run_data_preparation.py          # Prepare and validate data
python run_technical_indicators.py      # Calculate indicators
python regenerate_balanced_dataset.py   # Create balanced labels
python run_final_data_export.py        # Format for training
python example_lstm_training.py        # Train LSTM model
```

### 2. Live Trading Setup
```python
from master_config import get_config
from live_trading_system import LiveTradingSystem

# Initialize with production configuration
config = get_config("production")
trading_system = LiveTradingSystem(config)

# Start live trading (30-60 second intervals)
trading_system.start_live_trading()
```

### 3. Backtesting
```python
from backtesting_engine import BacktestEngine
from master_config import get_config

config = get_config("backtesting")
backtest = BacktestEngine(config)

results = backtest.run_backtest(
    start_date='2023-01-01',
    end_date='2024-12-31',
    initial_balance=10000
)

print(f"Total Return: {results['total_return']:.2%}")
print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
print(f"Max Drawdown: {results['max_drawdown']:.2%}")
```

### 4. Parameter Optimization
```python
from parameter_optimizer import ParameterOptimizer

optimizer = ParameterOptimizer()

# Define parameter ranges
param_ranges = {
    'target_labels.take_profit_pips': [20, 25, 30, 35],
    'target_labels.stop_loss_pips': [10, 12, 15, 18],
    'lstm_model.training.learning_rate': [0.0005, 0.001, 0.002]
}

# Run optimization
best_params = optimizer.optimize(param_ranges, metric='sharpe_ratio')
```

## Production Deployment

### System Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MetaTrader 5  │───▶│  Data Pipeline   │───▶│  LSTM Model     │
│   (Live Data)   │    │  (30-60 sec)     │    │  (Inference)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Risk Manager  │◀───│  Trade Executor  │◀───│  Signal Filter  │
│   (Position     │    │  (Order Mgmt)    │    │  (Confidence)   │
│    Sizing)      │    └──────────────────┘    └─────────────────┘
└─────────────────┘
```

### Deployment Checklist
- [ ] **Environment Setup**: Python 3.8+, required packages installed
- [ ] **MT5 Connection**: Valid login credentials, server configuration
- [ ] **Model Validation**: Trained model meets performance thresholds
- [ ] **Configuration Review**: Production parameters validated
- [ ] **Risk Controls**: Position sizing, maximum exposure limits
- [ ] **Monitoring Setup**: Logging, alerting, performance tracking
- [ ] **Backup Systems**: Model backup, configuration backup
- [ ] **Testing**: Paper trading validation before live deployment

### Monitoring and Maintenance
- **Real-time Monitoring**: Trade execution, model performance, system health
- **Daily Reports**: P&L, drawdown, signal accuracy, system uptime
- **Weekly Reviews**: Model performance, parameter adjustments
- **Monthly Retraining**: Update model with recent data
- **Quarterly Optimization**: Full parameter optimization cycle

### Error Handling and Failsafes
- **Connection Loss**: Automatic reconnection with exponential backoff
- **Model Errors**: Fallback to conservative hold signals
- **Risk Breaches**: Automatic position closure, trading halt
- **Data Quality Issues**: Skip corrupted data, alert operators
- **System Overload**: Graceful degradation, priority queuing

This documentation provides a complete reference for understanding, implementing, and maintaining the LSTM gold trading strategy system.
