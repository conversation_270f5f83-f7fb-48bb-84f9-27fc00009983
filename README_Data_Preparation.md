# XAUUSD Data Preparation Pipeline

## Overview

This comprehensive data preparation pipeline processes XAUUSD (Gold/USD) 5-minute historical data for use in LSTM-based trading strategies. The pipeline handles data validation, gap analysis, MetaTrader5 integration, and quality control.

## Features

### ✅ Completed Features

1. **Data Validation and Gap Analysis**
   - Loads existing CSV data with automatic format detection
   - Handles semicolon-separated CSV formats
   - Analyzes gaps in 5-minute intervals
   - Validates data integrity and OHLC relationships

2. **MetaTrader5 Integration**
   - Connects to MT5 terminal automatically
   - Retrieves missing XAUUSD! data (correct symbol)
   - <PERSON><PERSON> timezone conversion (UTC to NY time)
   - Merges new data with existing dataset

3. **Data Standardization**
   - Standardizes column names to: ['datetime', 'open', 'high', 'low', 'close', 'volume']
   - Ensures proper datetime formatting with timezone awareness
   - Filters data to NY trading hours (09:30-17:00)
   - Removes weekends and holidays

4. **Quality Control**
   - Validates OHLC relationships
   - Removes duplicate timestamps
   - <PERSON>les missing values appropriately
   - Logs all quality issues and resolutions

## Pipeline Results

### Dataset Summary
- **Total Records**: 484,717 (after filtering)
- **Date Range**: June 11, 2004 to September 24, 2025
- **Span**: 7,775 days (21+ years of data)
- **Price Range**: $382.50 - $3,788.80
- **Average Close**: $1,393.10

### Data Quality Report
- **Gaps Found**: 8,512 (expected due to market closures)
- **Invalid Records Removed**: 0
- **New Records Added**: 4,625 (from MT5 update)
- **Zero Volume Records**: 0

## Files Created

1. **`data_preparation_pipeline.py`** - Main pipeline implementation
2. **`run_data_preparation.py`** - Simple runner script
3. **`config.py`** - Configuration parameters
4. **`XAU_5m_data.csv`** - Processed dataset (updated)
5. **`data_preparation.log`** - Detailed execution log
6. **`data_preparation_report.json`** - Machine-readable report

## Usage

### Basic Usage
```bash
# Activate virtual environment
.\venv\Scripts\activate.ps1

# Run the pipeline
python run_data_preparation.py
```

### Advanced Usage
```python
from data_preparation_pipeline import XAUDataPreparationPipeline

# Initialize pipeline
pipeline = XAUDataPreparationPipeline("XAU_5m_data.csv")

# Run complete pipeline
report = pipeline.run_pipeline()

# Check results
if report['success']:
    print("Pipeline completed successfully!")
else:
    print(f"Pipeline failed: {report['error']}")
```

## Configuration

Key parameters can be adjusted in `config.py`:

```python
# Trading Hours (NY Time)
TRADING_HOURS = {
    'start_hour': 9,
    'start_minute': 30,
    'end_hour': 17,
    'end_minute': 0,
}

# MetaTrader5 Settings
DATA_CONFIG = {
    'mt5_symbol': 'XAUUSD!',  # Correct MT5 symbol
    'timeframe': 'M5',
    'max_history_days': 90,
}
```

## Data Quality Considerations

### Handled Issues
- ✅ Semicolon-separated CSV format
- ✅ Missing timezone information (assumed UTC)
- ✅ Weekend and holiday data filtering
- ✅ Duplicate timestamp removal
- ✅ OHLC relationship validation

### Expected Gaps
The 8,512 gaps found are expected and include:
- Weekend market closures (Friday 17:00 to Monday 09:30 NY time)
- Holiday market closures
- Early market closures on special days
- Broker-specific data gaps

## Next Steps

The processed dataset is now ready for:

1. **Technical Indicator Calculations**
   - MACD Histogram Slope
   - 14-period Stochastic %D
   - 10-period Detrended Price Oscillator (DPO)
   - 20-period Bias Ratio

2. **LSTM Sequence Generation**
   - 48-timestep sequences (4 hours of 5-minute data)
   - 5 features per timestep

3. **Target Label Generation**
   - Forward-looking analysis
   - Buy/Sell/Hold classification
   - Risk-based labeling (30 pips SL, 60 pips TP)

## Requirements

### Software Requirements
- Python 3.8+
- MetaTrader5 terminal (installed and logged in)
- Virtual environment with required packages

### Package Dependencies
- pandas >= 2.0.0
- numpy >= 1.20.0
- MetaTrader5 >= 5.0.0
- pytz >= 2021.1

### Data Requirements
- `XAU_5m_data.csv` file from Kaggle
- Active MT5 connection (optional, for updates)

## Troubleshooting

### Common Issues

1. **"XAU_5m_data.csv not found"**
   - Ensure the CSV file is in the current directory
   - Check file name spelling and case sensitivity

2. **"MT5 initialization failed"**
   - Ensure MetaTrader5 is installed and running
   - Check that you're logged into a trading account
   - Verify XAUUSD! symbol is available

3. **"Missing required columns"**
   - The pipeline auto-detects CSV format
   - Check that your CSV has the expected structure
   - Review the log for column detection details

### Performance Notes
- Processing 1.4M+ records takes ~30 seconds
- Memory usage peaks at ~500MB during processing
- Final dataset is ~50MB compressed

## Logging

Detailed logs are written to `data_preparation.log` with:
- Timestamp for each operation
- Data quality metrics
- Error messages and warnings
- Performance statistics

## Support

For issues or questions:
1. Check the log file for detailed error messages
2. Verify all prerequisites are met
3. Review the configuration parameters
4. Ensure data file format matches expectations
