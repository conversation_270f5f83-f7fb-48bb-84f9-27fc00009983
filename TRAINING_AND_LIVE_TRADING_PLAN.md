# LSTM Gold Trading Strategy - Training and Live Trading Integration Plan

## Executive Summary

This document outlines the complete roadmap for training the LSTM model and integrating it into a live trading system for XAUUSD. The plan addresses the critical class imbalance issue and provides a production-ready framework for automated gold trading.

## Phase A: Training Phase

### A1. Dataset Requirements and Validation

#### Minimum Dataset Requirements
- **Minimum Samples**: 1,000+ sequences for robust training
- **Current Dataset**: 714 sequences (acceptable but minimal)
- **Recommended**: 2,000+ sequences for optimal performance
- **Class Distribution**: 
  - Current: Buy 17.9%, Sell 64.4%, Hold 17.6%
  - Status: ✅ **ACCEPTABLE** - Reflects market reality

#### Data Quality Validation
```python
# Validation Checklist
✅ Temporal ordering preserved
✅ No data leakage between splits
✅ Market gaps properly handled
✅ Feature normalization validated
✅ Label generation logic verified
✅ Class imbalance addressed
```

### A2. Model Architecture Specifications

#### Recommended LSTM Architecture
```python
model = Sequential([
    # Input: (batch_size, 48, 5)
    LSTM(64, return_sequences=True, input_shape=(48, 5)),
    Dropout(0.2),
    LSTM(32, return_sequences=False),
    Dropout(0.2),
    Dense(16, activation='relu'),
    <PERSON><PERSON>(3, activation='softmax')  # hold, buy, sell
])

# Total Parameters: ~50,000
# Memory Usage: ~200MB during training
```

#### Alternative Architectures for Testing
1. **Lightweight Model** (for faster inference):
   - LSTM(32) → LSTM(16) → Dense(8) → Dense(3)
   - Parameters: ~15,000

2. **Enhanced Model** (if more data available):
   - LSTM(128) → LSTM(64) → LSTM(32) → Dense(16) → Dense(3)
   - Parameters: ~150,000

### A3. Training Hyperparameters and Strategy

#### Optimal Training Configuration
```python
training_config = {
    'batch_size': 32,  # Balanced for 714 samples
    'epochs': 100,
    'learning_rate': 0.001,
    'optimizer': 'adam',
    'loss': 'categorical_crossentropy',
    'metrics': ['accuracy', 'precision', 'recall'],
    'validation_split': 0.0,  # Use separate validation set
    'shuffle': False,  # Maintain temporal order
    'class_weight': {  # Address class imbalance
        0: 1.0,  # hold
        1: 3.6,  # buy (boost underrepresented class)
        2: 0.3   # sell (reduce overrepresented class)
    }
}
```

#### Advanced Training Techniques
1. **Learning Rate Scheduling**:
   ```python
   lr_scheduler = ReduceLROnPlateau(
       monitor='val_loss',
       factor=0.5,
       patience=5,
       min_lr=0.0001
   )
   ```

2. **Early Stopping**:
   ```python
   early_stopping = EarlyStopping(
       monitor='val_loss',
       patience=10,
       restore_best_weights=True
   )
   ```

3. **Model Checkpointing**:
   ```python
   checkpoint = ModelCheckpoint(
       'best_model.h5',
       monitor='val_accuracy',
       save_best_only=True
   )
   ```

### A4. Performance Metrics and Acceptance Criteria

#### Primary Metrics
- **Accuracy**: Target ≥56%, Minimum ≥52%
- **Precision**: Target ≥55%, Minimum ≥50%
- **Recall**: Target ≥50%, Minimum ≥45%
- **F1-Score**: Target ≥52%, Minimum ≥47%

#### Trading-Specific Metrics
- **Sharpe Ratio**: Target ≥1.3, Minimum ≥1.0
- **Maximum Drawdown**: Target ≤10%, Maximum ≤15%
- **Win Rate**: Target ≥55%, Minimum ≥50%
- **Risk-Reward Ratio**: Target ≥1.8, Minimum ≥1.5

#### Model Validation Process
```python
def validate_model_performance(model, test_data):
    """Comprehensive model validation"""
    
    # 1. Statistical Validation
    predictions = model.predict(test_data['X'])
    accuracy = accuracy_score(test_data['y'], predictions)
    
    # 2. Trading Simulation
    backtest_results = simulate_trading(predictions, test_data)
    sharpe_ratio = calculate_sharpe_ratio(backtest_results)
    
    # 3. Stability Testing
    stability_score = test_prediction_stability(model, test_data)
    
    return {
        'accuracy': accuracy,
        'sharpe_ratio': sharpe_ratio,
        'stability_score': stability_score,
        'meets_criteria': all([
            accuracy >= 0.52,
            sharpe_ratio >= 1.0,
            stability_score >= 0.8
        ])
    }
```

### A5. Backtesting Methodology

#### Walk-Forward Analysis
```python
# 12-month walk-forward validation
for month in range(12):
    train_end = start_date + timedelta(days=30*month)
    test_start = train_end
    test_end = test_start + timedelta(days=30)
    
    # Train on historical data
    model.fit(train_data)
    
    # Test on next month
    predictions = model.predict(test_data)
    results[month] = evaluate_performance(predictions)
```

#### Out-of-Sample Testing
- **Training Period**: 70% of data (earliest)
- **Validation Period**: 15% of data (middle)
- **Test Period**: 15% of data (most recent)
- **Forward Testing**: 30 days of paper trading

## Phase B: Live Trading Integration

### B1. Real-Time Data Pipeline Architecture

#### Data Flow Design
```
MetaTrader 5 → Data Collector → Feature Calculator → Model Inference → Signal Filter → Trade Executor
     ↓              ↓                ↓                 ↓              ↓             ↓
  Raw OHLCV    Normalized     Technical         Predictions    Risk-Filtered   Executed
   (5-min)      Data Store    Indicators        (30-60 sec)     Signals        Orders
```

#### Implementation Components
1. **MT5 Data Collector**:
   ```python
   class MT5DataCollector:
       def __init__(self):
           self.symbol = "XAUUSD!"
           self.timeframe = mt5.TIMEFRAME_M5
           
       def get_latest_data(self, bars=48):
           """Get latest 48 bars for LSTM input"""
           rates = mt5.copy_rates_from_pos(
               self.symbol, self.timeframe, 0, bars
           )
           return self.format_data(rates)
   ```

2. **Real-Time Feature Calculator**:
   ```python
   class RealTimeFeatureCalculator:
       def __init__(self):
           self.indicators = TechnicalIndicators()
           
       def calculate_features(self, ohlcv_data):
           """Calculate all 5 technical indicators"""
           features = {
               'macd_slope': self.indicators.calculate_macd_histogram_slope(ohlcv_data),
               'stoch_d': self.indicators.calculate_stochastic_d(ohlcv_data),
               'dpo': self.indicators.calculate_dpo(ohlcv_data),
               'bias_ratio': self.indicators.calculate_bias_ratio(ohlcv_data),
               'log_returns': self.indicators.calculate_log_returns(ohlcv_data)
           }
           return self.normalize_features(features)
   ```

### B2. Model Inference Workflow

#### Real-Time Prediction Pipeline
```python
class LiveTradingSystem:
    def __init__(self, model_path, config):
        self.model = load_model(model_path)
        self.config = config
        self.last_prediction_time = None
        
    def run_inference_cycle(self):
        """30-60 second inference cycle"""
        
        # 1. Collect latest data
        data = self.data_collector.get_latest_data()
        
        # 2. Calculate features
        features = self.feature_calculator.calculate_features(data)
        
        # 3. Make prediction
        prediction = self.model.predict(features.reshape(1, 48, 5))
        confidence = np.max(prediction)
        signal = np.argmax(prediction)  # 0=hold, 1=buy, 2=sell
        
        # 4. Apply filters
        if self.should_trade(signal, confidence):
            self.execute_trade(signal, confidence)
            
        # 5. Update monitoring
        self.update_performance_metrics(signal, confidence)
```

#### Prediction Confidence Filtering
```python
def should_trade(self, signal, confidence):
    """Advanced signal filtering"""
    
    # Minimum confidence threshold
    if confidence < self.config['risk_management']['filters']['min_confidence']:
        return False
    
    # Spread check
    current_spread = self.get_current_spread()
    if current_spread > self.config['risk_management']['filters']['max_spread_pips']:
        return False
    
    # Position limits
    if self.get_open_positions() >= self.config['risk_management']['position_sizing']['max_positions']:
        return False
    
    # News filter
    if self.is_news_time():
        return False
        
    return True
```

### B3. Trade Execution Logic

#### Order Management System
```python
class TradeExecutor:
    def __init__(self, mt5_connection, risk_manager):
        self.mt5 = mt5_connection
        self.risk_manager = risk_manager
        
    def execute_trade(self, signal, confidence):
        """Execute trade with full risk management"""
        
        # 1. Calculate position size
        position_size = self.risk_manager.calculate_position_size(
            account_balance=self.get_account_balance(),
            risk_per_trade=0.0025,  # 0.25%
            stop_loss_pips=12
        )
        
        # 2. Determine order parameters
        if signal == 1:  # Buy
            order_type = mt5.ORDER_TYPE_BUY
            sl_distance = -12  # pips
            tp_distance = 25   # pips
        elif signal == 2:  # Sell
            order_type = mt5.ORDER_TYPE_SELL
            sl_distance = 12   # pips
            tp_distance = -25  # pips
        else:
            return  # Hold signal
        
        # 3. Place order with error handling
        try:
            result = self.place_market_order(
                symbol="XAUUSD!",
                order_type=order_type,
                volume=position_size,
                sl_distance=sl_distance,
                tp_distance=tp_distance
            )
            
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                self.log_successful_trade(result, signal, confidence)
            else:
                self.log_failed_trade(result, signal, confidence)
                
        except Exception as e:
            self.handle_execution_error(e, signal, confidence)
```

### B4. Risk Management Integration

#### Position Sizing Algorithm
```python
def calculate_position_size(account_balance, risk_per_trade, stop_loss_pips):
    """Kelly Criterion-based position sizing"""
    
    # Base risk amount
    risk_amount = account_balance * risk_per_trade
    
    # Convert pips to price movement
    pip_value = 0.01  # For XAUUSD
    price_risk = stop_loss_pips * pip_value
    
    # Calculate position size
    position_size = risk_amount / price_risk
    
    # Apply maximum position limits
    max_position = account_balance * 0.1  # 10% max exposure
    position_size = min(position_size, max_position)
    
    return round(position_size, 2)
```

#### Dynamic Risk Adjustment
```python
class DynamicRiskManager:
    def __init__(self):
        self.base_risk = 0.0025  # 0.25%
        self.performance_tracker = PerformanceTracker()
        
    def adjust_risk_based_on_performance(self):
        """Adjust risk based on recent performance"""
        
        recent_performance = self.performance_tracker.get_recent_stats(days=7)
        
        if recent_performance['win_rate'] > 0.6:
            # Increase risk slightly for good performance
            self.current_risk = min(self.base_risk * 1.2, 0.005)
        elif recent_performance['win_rate'] < 0.4:
            # Decrease risk for poor performance
            self.current_risk = max(self.base_risk * 0.8, 0.001)
        else:
            self.current_risk = self.base_risk
            
        return self.current_risk
```

### B5. Monitoring and Alerting Systems

#### Real-Time Performance Monitoring
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0,
            'max_drawdown': 0,
            'current_drawdown': 0
        }
        
    def update_metrics(self, trade_result):
        """Update performance metrics after each trade"""
        
        self.metrics['total_trades'] += 1
        
        if trade_result['pnl'] > 0:
            self.metrics['winning_trades'] += 1
            
        self.metrics['total_pnl'] += trade_result['pnl']
        
        # Update drawdown
        if self.metrics['total_pnl'] < self.metrics['peak_balance']:
            self.metrics['current_drawdown'] = (
                self.metrics['peak_balance'] - self.metrics['total_pnl']
            ) / self.metrics['peak_balance']
            
            self.metrics['max_drawdown'] = max(
                self.metrics['max_drawdown'],
                self.metrics['current_drawdown']
            )
        else:
            self.metrics['peak_balance'] = self.metrics['total_pnl']
            self.metrics['current_drawdown'] = 0
            
        # Check for alerts
        self.check_alert_conditions()
```

#### Alert System
```python
def check_alert_conditions(self):
    """Check for conditions requiring immediate attention"""
    
    alerts = []
    
    # Drawdown alert
    if self.metrics['current_drawdown'] > 0.10:  # 10%
        alerts.append({
            'type': 'HIGH_DRAWDOWN',
            'message': f"Current drawdown: {self.metrics['current_drawdown']:.1%}",
            'severity': 'HIGH'
        })
    
    # Win rate alert
    if self.metrics['total_trades'] > 20:
        win_rate = self.metrics['winning_trades'] / self.metrics['total_trades']
        if win_rate < 0.4:
            alerts.append({
                'type': 'LOW_WIN_RATE',
                'message': f"Win rate dropped to {win_rate:.1%}",
                'severity': 'MEDIUM'
            })
    
    # Send alerts
    for alert in alerts:
        self.send_alert(alert)
```

## Phase C: Production Deployment

### C1. System Architecture for Live Trading

#### High-Level Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    PRODUCTION ENVIRONMENT                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   MT5 API   │  │ Data Store  │  │   LSTM Model        │  │
│  │ Connection  │  │ (Redis/DB)  │  │   (TensorFlow)      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│         │                │                     │            │
│         ▼                ▼                     ▼            │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │            TRADING ENGINE (Main Process)               │  │
│  │  • Data Collection (30-60 sec cycle)                  │  │
│  │  • Feature Calculation                                │  │
│  │  • Model Inference                                    │  │
│  │  • Signal Filtering                                   │  │
│  │  • Risk Management                                    │  │
│  │  • Trade Execution                                    │  │
│  └─────────────────────────────────────────────────────────┘  │
│         │                                                    │
│         ▼                                                    │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │              MONITORING & LOGGING                      │  │
│  │  • Performance Tracking                               │  │
│  │  • Error Logging                                      │  │
│  │  • Alert System                                       │  │
│  │  • Daily Reports                                      │  │
│  └─────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### C2. Error Handling and Failsafe Mechanisms

#### Connection Management
```python
class RobustMT5Connection:
    def __init__(self):
        self.max_retries = 3
        self.retry_delay = 5  # seconds
        
    def connect_with_retry(self):
        """Robust connection with exponential backoff"""
        for attempt in range(self.max_retries):
            try:
                if mt5.initialize():
                    return True
            except Exception as e:
                wait_time = self.retry_delay * (2 ** attempt)
                time.sleep(wait_time)
                
        return False
```

#### Graceful Degradation
```python
def handle_model_error(self, error):
    """Handle model inference errors gracefully"""
    
    # Log error
    self.logger.error(f"Model inference failed: {error}")
    
    # Switch to conservative mode
    self.trading_mode = "CONSERVATIVE"
    
    # Close existing positions if critical error
    if self.is_critical_error(error):
        self.close_all_positions()
        
    # Send alert
    self.send_alert({
        'type': 'MODEL_ERROR',
        'message': str(error),
        'severity': 'HIGH'
    })
```

### C3. Performance Monitoring and Model Retraining

#### Automated Model Retraining
```python
class ModelRetrainingScheduler:
    def __init__(self):
        self.retrain_frequency = 30  # days
        self.performance_threshold = 0.5  # accuracy
        
    def should_retrain(self):
        """Determine if model needs retraining"""
        
        # Time-based retraining
        days_since_training = (datetime.now() - self.last_training_date).days
        if days_since_training >= self.retrain_frequency:
            return True
            
        # Performance-based retraining
        recent_accuracy = self.get_recent_accuracy(days=7)
        if recent_accuracy < self.performance_threshold:
            return True
            
        return False
        
    def retrain_model(self):
        """Automated model retraining process"""
        
        # 1. Collect recent data
        new_data = self.collect_recent_data(days=90)
        
        # 2. Prepare training data
        X, y = self.prepare_training_data(new_data)
        
        # 3. Retrain model
        new_model = self.train_new_model(X, y)
        
        # 4. Validate new model
        if self.validate_new_model(new_model):
            self.deploy_new_model(new_model)
        else:
            self.logger.warning("New model failed validation, keeping current model")
```

This comprehensive plan provides a complete roadmap for training the LSTM model and deploying it in a production trading environment with proper risk management, monitoring, and failsafe mechanisms.
