{"regeneration_timestamp": "2025-09-24T12:04:46.509974", "market_analysis": {"date_range": {"start": "2022-09-26 13:30:00", "end": "2025-09-24 18:20:00", "days": 1094}, "price_analysis": {"start_price": 1639.42, "end_price": 3755.91, "total_return": 1.2909992558343804, "min_price": 1616.94, "max_price": 3788.06, "price_range_pct": 0.9136371053090576}, "volatility_analysis": {"daily_volatility": 0.000934684347113178, "annualized_volatility": 0.2518033386008161, "max_daily_return": 0.02545344263614102, "min_daily_return": -0.03783250052362286}, "market_regime": "trending_up"}, "generation_report": {"sequence_generation": {"input_data": {"total_records": 70264, "date_range": {"start": "2022-09-26 13:30:00", "end": "2025-09-24 18:20:00"}, "features_used": ["macd_histogram_slope_norm", "stochastic_d_norm", "dpo_norm", "bias_ratio_norm", "log_returns_norm"]}, "generation_stats": {"total_potential": 1463, "valid_generated": 714, "rejected": 749, "rejection_reasons": {"contains_major_gap": 749}, "final_shape": [714, 48, 5], "memory_usage_mb": 0.6536865234375}, "validation_results": {"status": "success", "sequence_count": 714, "sequence_shape": [714, 48, 5], "feature_statistics": {"macd_histogram_slope_norm": {"mean": 0.0015279046492651105, "std": 0.6017740964889526, "min": -1.0, "max": 1.0, "nan_count": 0}, "stochastic_d_norm": {"mean": 0.0418514683842659, "std": 0.5444906949996948, "min": -0.9818375706672668, "max": 0.9807150363922119, "nan_count": 0}, "dpo_norm": {"mean": 0.009579678997397423, "std": 0.6086316108703613, "min": -1.0, "max": 1.0, "nan_count": 0}, "bias_ratio_norm": {"mean": -0.003982142545282841, "std": 0.4249859154224396, "min": -1.0, "max": 1.0, "nan_count": 0}, "log_returns_norm": {"mean": -0.000738777918741107, "std": 0.42916110157966614, "min": -1.0, "max": 1.0, "nan_count": 0}}, "quality_checks": {"all_finite": "True", "proper_range": "True", "no_constant_sequences": true}, "issues": []}, "configuration": {"sequence_length": 48, "features_per_timestep": 5, "min_gap_minutes": 10, "max_gap_hours": 72, "overlap_ratio": 0.0, "validation_checks": true, "memory_efficient": true}, "sequence_statistics": {"total_sequences": 1463, "valid_sequences": 714, "rejected_sequences": 749, "rejection_reasons": {"contains_major_gap": 749}, "feature_statistics": {}}}, "label_generation": {"total_sequences": 714, "label_distribution": {"buy": 128, "hold": 126, "sell": 460}, "generation_stats": {"total_analyzed": 714, "buy_signals": 128, "sell_signals": 460, "hold_signals": 0, "filtered_by_volatility": 126, "filtered_by_confidence": 0}, "configuration": {"take_profit_pips": 25, "stop_loss_pips": 12, "time_horizon_bars": 8, "risk_reward_ratio": 2.0, "pip_value": 0.01, "min_price_movement": 0.02, "hold_threshold": 0.4, "buy_confidence_threshold": 0.3, "sell_confidence_threshold": 0.6, "volatility_threshold": 0.3, "trend_strength_threshold": 0.2, "label_encoding": {"hold": 0, "buy": 1, "sell": 2}}, "generation_timestamp": "2025-09-24T12:04:46.328060"}, "balanced_config": {"take_profit_pips": 25, "stop_loss_pips": 12, "time_horizon_bars": 8, "risk_reward_ratio": 2.0, "pip_value": 0.01, "min_price_movement": 0.02, "hold_threshold": 0.4, "buy_confidence_threshold": 0.3, "sell_confidence_threshold": 0.6, "volatility_threshold": 0.3, "trend_strength_threshold": 0.2, "label_encoding": {"hold": 0, "buy": 1, "sell": 2}}}, "validation_results": {"actual_distribution": {"buy": {"count": 128, "percentage": 17.92717086834734}, "hold": {"count": 126, "percentage": 17.647058823529413}, "sell": {"count": 460, "percentage": 64.42577030812325}}, "target_ranges": {"buy": [15.0, 30.0], "sell": [15.0, 70.0], "hold": [15.0, 40.0]}, "meets_requirements": true, "issues": [], "recommendations": []}, "formatting_results": {"status": "success", "input_validation": {"status": "success", "issues": [], "warnings": [], "data_info": {"sequences_shape": [714, 48, 5], "labels_shape": [714, 3], "timestamps_shape": [714], "sequences_dtype": "float32", "labels_dtype": "float32", "memory_usage_mb": 0.6673049926757812}}, "split_validation": {"status": "success", "issues": [], "warnings": [], "split_analysis": {"train": {"sample_count": 499, "X_shape": [499, 48, 5], "y_shape": [499, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.4625587463378906, "temporal_span_days": 758, "class_distribution": [87.0, 96.0, 316.0]}, "validation": {"sample_count": 107, "X_shape": [107, 48, 5], "y_shape": [107, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.09918594360351562, "temporal_span_days": 167, "class_distribution": [22.0, 20.0, 65.0]}, "test": {"sample_count": 108, "X_shape": [108, 48, 5], "y_shape": [108, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.1001129150390625, "temporal_span_days": 165, "class_distribution": [17.0, 12.0, 79.0]}}}, "exported_files": {"train": "balanced_lstm_data\\lstm_train_final.npz", "validation": "balanced_lstm_data\\lstm_validation_final.npz", "test": "balanced_lstm_data\\lstm_test_final.npz", "hdf5": "balanced_lstm_data\\lstm_dataset_final.h5", "metadata": "balanced_lstm_data\\lstm_training_metadata.json"}, "metadata": {"dataset_info": {"total_samples": 714, "sequence_length": 48, "feature_count": 5, "num_classes": 3, "total_memory_mb": 0.6618576049804688}, "splits": {"train": {"sample_count": 499, "percentage": 0.6988795518207283, "X_shape": [499, 48, 5], "y_shape": [499, 3], "memory_mb": 0.4625587463378906, "date_range": {"start": "2022-09-26 17:25:00", "end": "2024-10-23 19:30:00", "span_days": 758}}, "validation": {"sample_count": 107, "percentage": 0.14985994397759103, "X_shape": [107, 48, 5], "y_shape": [107, 3], "memory_mb": 0.09918594360351562, "date_range": {"start": "2024-10-24 19:55:00", "end": "2025-04-09 20:05:00", "span_days": 167}}, "test": {"sample_count": 108, "percentage": 0.15126050420168066, "X_shape": [108, 48, 5], "y_shape": [108, 3], "memory_mb": 0.1001129150390625, "date_range": {"start": "2025-04-10 20:30:00", "end": "2025-09-23 18:35:00", "span_days": 165}}}, "data_quality": {"status": "success", "issues": [], "warnings": [], "split_analysis": {"train": {"sample_count": 499, "X_shape": [499, 48, 5], "y_shape": [499, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.4625587463378906, "temporal_span_days": 758, "class_distribution": [87.0, 96.0, 316.0]}, "validation": {"sample_count": 107, "X_shape": [107, 48, 5], "y_shape": [107, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.09918594360351562, "temporal_span_days": 167, "class_distribution": [22.0, 20.0, 65.0]}, "test": {"sample_count": 108, "X_shape": [108, 48, 5], "y_shape": [108, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.1001129150390625, "temporal_span_days": 165, "class_distribution": [17.0, 12.0, 79.0]}}}, "recommended_training_params": {"batch_size": 49, "epochs": 100, "early_stopping_patience": 10, "learning_rate": 0.001, "validation_split": 0.0, "shuffle": false}, "export_info": {"export_timestamp": "2025-09-24T12:04:46.508448", "formatter_version": "1.0", "configuration": {"splits": {"train_ratio": 0.7, "validation_ratio": 0.15, "test_ratio": 0.15, "temporal_ordering": true}, "export_formats": {"npz": true, "hdf5": true, "compression": true}, "validation": {"check_shapes": true, "check_data_types": true, "check_temporal_order": true, "check_label_distribution": true}, "memory_optimization": {"use_float32": true, "batch_processing": true, "clear_intermediate": true}}}}, "formatting_stats": {"original_shape": [714, 48, 5], "final_shapes": {"train": [499, 48, 5], "validation": [107, 48, 5], "test": [108, 48, 5]}, "split_info": {"train": {"sample_count": 499, "percentage": 0.6988795518207283, "date_range": {"start": "2022-09-26 17:25:00", "end": "2024-10-23 19:30:00"}}, "validation": {"sample_count": 107, "percentage": 0.14985994397759103, "date_range": {"start": "2024-10-24 19:55:00", "end": "2025-04-09 20:05:00"}}, "test": {"sample_count": 108, "percentage": 0.15126050420168066, "date_range": {"start": "2025-04-10 20:30:00", "end": "2025-09-23 18:35:00"}}}, "export_info": {"train": "balanced_lstm_data\\lstm_train_final.npz", "validation": "balanced_lstm_data\\lstm_validation_final.npz", "test": "balanced_lstm_data\\lstm_test_final.npz", "hdf5": "balanced_lstm_data\\lstm_dataset_final.h5", "metadata": "balanced_lstm_data\\lstm_training_metadata.json"}, "validation_results": {"input": {"status": "success", "issues": [], "warnings": [], "data_info": {"sequences_shape": [714, 48, 5], "labels_shape": [714, 3], "timestamps_shape": [714], "sequences_dtype": "float32", "labels_dtype": "float32", "memory_usage_mb": 0.6673049926757812}}, "splits": {"status": "success", "issues": [], "warnings": [], "split_analysis": {"train": {"sample_count": 499, "X_shape": [499, 48, 5], "y_shape": [499, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.4625587463378906, "temporal_span_days": 758, "class_distribution": [87.0, 96.0, 316.0]}, "validation": {"sample_count": 107, "X_shape": [107, 48, 5], "y_shape": [107, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.09918594360351562, "temporal_span_days": 167, "class_distribution": [22.0, 20.0, 65.0]}, "test": {"sample_count": 108, "X_shape": [108, 48, 5], "y_shape": [108, 3], "X_dtype": "float32", "y_dtype": "float32", "memory_mb": 0.1001129150390625, "temporal_span_days": 165, "class_distribution": [17.0, 12.0, 79.0]}}}}}}, "configuration": {"years_back": 3, "target_ranges": {"buy": [15.0, 30.0], "sell": [15.0, 70.0], "hold": [15.0, 40.0]}, "label_config": {"take_profit_pips": 25, "stop_loss_pips": 12, "time_horizon_bars": 8, "risk_reward_ratio": 2.0, "pip_value": 0.01, "min_price_movement": 0.02, "hold_threshold": 0.4, "buy_confidence_threshold": 0.3, "sell_confidence_threshold": 0.6, "volatility_threshold": 0.3, "trend_strength_threshold": 0.2, "label_encoding": {"hold": 0, "buy": 1, "sell": 2}}}}