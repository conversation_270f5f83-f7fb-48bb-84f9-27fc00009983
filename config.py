"""
Configuration file for XAUUSD LSTM Trading Strategy Data Pipeline
================================================================

This file contains all configurable parameters for the data preparation pipeline
and subsequent technical indicator calculations.

Author: AI Assistant
Date: 2025-01-24
"""

# Data Source Configuration
DATA_CONFIG = {
    # Input/Output file paths
    'csv_file_path': 'XAU_5m_data.csv',
    'output_file_path': 'XAU_5m_data.csv',  # Same as input to update in place
    'log_file_path': 'data_preparation.log',
    'report_file_path': 'data_preparation_report.json',
    
    # MetaTrader5 Configuration
    'mt5_symbol': 'XAUUSD!',  # Correct symbol for MT5 (not XAUUSD)
    'timeframe': 'M5',  # 5-minute timeframe
    'max_history_days': 90,  # Maximum days to retrieve in single request
    
    # Data Quality Parameters
    'allow_zero_volume': True,  # Whether to keep zero volume bars
    'max_gap_minutes': 60,  # Maximum acceptable gap in minutes
    'price_change_threshold': 0.1,  # Maximum acceptable price change (10%)
}

# Trading Hours Configuration (NY Time)
TRADING_HOURS = {
    'timezone': 'America/New_York',
    'start_hour': 9,
    'start_minute': 30,
    'end_hour': 17,
    'end_minute': 0,
    'trading_days': [0, 1, 2, 3, 4],  # Monday=0 to Friday=4
}

# Technical Indicators Configuration
INDICATORS_CONFIG = {
    # MACD Histogram Slope
    'macd': {
        'fast_period': 12,
        'slow_period': 26,
        'signal_period': 9,
        'slope_periods': 5,  # Number of bars for slope calculation
    },
    
    # Stochastic Oscillator
    'stochastic': {
        'k_period': 14,
        'd_period': 3,
        'smooth_k': 3,
    },
    
    # Detrended Price Oscillator (DPO)
    'dpo': {
        'period': 10,
    },
    
    # Bias Ratio
    'bias_ratio': {
        'sma_period': 20,
    },
    
    # Log Returns
    'log_returns': {
        'periods': 1,  # 1-period log returns
    }
}

# LSTM Model Configuration
LSTM_CONFIG = {
    # Sequence Parameters
    'lookback_window': 48,  # 48 bars = 4 hours of 5-minute data
    'features_per_timestep': 5,  # 4 indicators + 1 log return
    
    # Target Generation Parameters
    'risk_per_trade': 0.0025,  # 0.25% of equity
    'stop_loss_pips': 30,  # Stop loss in pips
    'take_profit_pips': 60,  # Take profit in pips (2:1 R:R ratio)
    'max_holding_bars': 12,  # Maximum holding period (1 hour)
    
    # Data Splits
    'train_ratio': 0.7,
    'validation_ratio': 0.15,
    'test_ratio': 0.15,
    
    # Normalization
    'normalization_method': 'minmax',  # 'minmax' or 'standard'
    'feature_range': (-1, 1),  # Range for MinMaxScaler
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'console_output': True,
    'file_output': True,
}

# Validation Thresholds
VALIDATION_CONFIG = {
    'min_records_required': 10000,  # Minimum records for training
    'max_missing_data_ratio': 0.05,  # Maximum 5% missing data allowed
    'max_outlier_ratio': 0.01,  # Maximum 1% outliers allowed
    'price_decimal_places': 2,  # Decimal places for price validation
}

# Performance Targets (for reference)
PERFORMANCE_TARGETS = {
    'target_accuracy': 0.56,  # 56% classification accuracy
    'target_risk_reward': 1.3,  # Average R:R ratio
    'max_drawdown': 0.15,  # Maximum 15% drawdown
    'min_sharpe_ratio': 1.2,  # Minimum Sharpe ratio
}

# File Paths for Pipeline Outputs
OUTPUT_PATHS = {
    'processed_data': 'XAU_5m_data.csv',
    'features_data': 'XAU_features.npz',
    'sequences_data': 'XAU_sequences.npz',
    'labels_data': 'XAU_labels.npz',
    'scaler_objects': 'scalers.pkl',
    'train_data': 'train_data.npz',
    'validation_data': 'validation_data.npz',
    'test_data': 'test_data.npz',
}

# Error Handling Configuration
ERROR_CONFIG = {
    'max_retries': 3,
    'retry_delay_seconds': 5,
    'continue_on_warnings': True,
    'strict_validation': False,  # Set to True for production
}

def get_config(section: str = None):
    """
    Get configuration parameters.
    
    Args:
        section: Specific configuration section to return
        
    Returns:
        Configuration dictionary or specific section
    """
    all_config = {
        'data': DATA_CONFIG,
        'trading_hours': TRADING_HOURS,
        'indicators': INDICATORS_CONFIG,
        'lstm': LSTM_CONFIG,
        'logging': LOGGING_CONFIG,
        'validation': VALIDATION_CONFIG,
        'performance': PERFORMANCE_TARGETS,
        'output_paths': OUTPUT_PATHS,
        'error_handling': ERROR_CONFIG,
    }
    
    if section:
        return all_config.get(section, {})
    
    return all_config

def validate_config():
    """
    Validate configuration parameters for consistency.
    
    Returns:
        bool: True if configuration is valid, False otherwise
    """
    errors = []
    
    # Validate trading hours
    if TRADING_HOURS['start_hour'] >= TRADING_HOURS['end_hour']:
        errors.append("Trading start hour must be before end hour")
    
    # Validate LSTM ratios
    total_ratio = LSTM_CONFIG['train_ratio'] + LSTM_CONFIG['validation_ratio'] + LSTM_CONFIG['test_ratio']
    if abs(total_ratio - 1.0) > 0.001:
        errors.append(f"LSTM data split ratios must sum to 1.0, got {total_ratio}")
    
    # Validate risk parameters
    if LSTM_CONFIG['stop_loss_pips'] <= 0 or LSTM_CONFIG['take_profit_pips'] <= 0:
        errors.append("Stop loss and take profit must be positive")
    
    # Validate lookback window
    if LSTM_CONFIG['lookback_window'] < 10:
        errors.append("Lookback window should be at least 10 bars")
    
    if errors:
        print("Configuration validation errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    return True

if __name__ == "__main__":
    # Test configuration validation
    if validate_config():
        print("✅ Configuration validation passed")
    else:
        print("❌ Configuration validation failed")
