2025-09-24 11:20:31,110 - INFO - Starting XAUUSD data preparation pipeline
2025-09-24 11:20:31,110 - INFO - Loading existing data from XAU_5m_data.csv
2025-09-24 11:20:32,615 - INFO - Successfully loaded 1413022 records from CSV
2025-09-24 11:20:32,616 - INFO - Data structure preview:
2025-09-24 11:20:32,616 - INFO - Columns: ['Date;Open;High;Low;Close;Volume']
2025-09-24 11:20:32,620 - INFO - First 3 rows:
              Date;Open;High;Low;Close;Volume
0        2004.06.11 07:15;384;384.1;384;384;3
1  2004.06.11 07:20;384.1;384.1;383.8;383.8;3
2  2004.06.11 07:25;383.8;384.3;383.8;384.3;6
2025-09-24 11:20:32,627 - INFO - Standardizing column names
2025-09-24 11:20:32,679 - ERROR - Missing required columns: ['datetime', 'open', 'high', 'low', 'close', 'volume']
2025-09-24 11:20:32,679 - INFO - Available columns: ['Date;Open;High;Low;Close;Volume']
2025-09-24 11:20:32,680 - ERROR - Pipeline failed: Missing required columns: ['datetime', 'open', 'high', 'low', 'close', 'volume']
2025-09-24 11:21:19,687 - INFO - Starting XAUUSD data preparation pipeline
2025-09-24 11:21:19,688 - INFO - Loading existing data from XAU_5m_data.csv
2025-09-24 11:21:19,689 - INFO - First line of CSV: Date;Open;High;Low;Close;Volume
2025-09-24 11:21:19,689 - INFO - Detected semicolon-separated format
2025-09-24 11:21:21,006 - INFO - Successfully loaded 1413022 records from CSV
2025-09-24 11:21:21,006 - INFO - Data structure preview:
2025-09-24 11:21:21,007 - INFO - Columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
2025-09-24 11:21:21,016 - INFO - First 3 rows:
               Date   Open   High    Low  Close  Volume
0  2004.06.11 07:15  384.0  384.1  384.0  384.0       3
1  2004.06.11 07:20  384.1  384.1  383.8  383.8       3
2  2004.06.11 07:25  383.8  384.3  383.8  384.3       6
2025-09-24 11:21:21,016 - INFO - Standardizing column names
2025-09-24 11:21:21,291 - INFO - Standardized columns: ['datetime', 'open', 'high', 'low', 'close', 'volume']
2025-09-24 11:21:21,305 - INFO - Parsing and standardizing datetime column
2025-09-24 11:21:22,034 - INFO - No timezone info found, assuming UTC
2025-09-24 11:21:22,209 - INFO - Date range: 2004-06-11 07:15:00+00:00 to 2025-07-15 19:45:00+00:00
2025-09-24 11:21:22,212 - INFO - Validating data quality
2025-09-24 11:21:22,433 - INFO - Filtering data to NY trading hours (09:30-17:00)
2025-09-24 11:21:23,045 - INFO - Filtered from 1413022 to 480093 records (trading hours only)
2025-09-24 11:21:23,056 - INFO - Analyzing gaps in 5-minute intervals
2025-09-24 11:21:23,403 - INFO - Found 8512 gaps in data
2025-09-24 11:21:23,403 - INFO - Largest gap: 6930.0 minutes
2025-09-24 11:21:23,409 - INFO - Data appears outdated, attempting to update from MT5
2025-09-24 11:21:23,410 - INFO - Connecting to MetaTrader5
2025-09-24 11:21:23,417 - INFO - Connected to MT5 - Account: ********
2025-09-24 11:21:23,417 - INFO - Symbol XAUUSD! found - Gold vs US Dollar / Spot
2025-09-24 11:21:23,417 - INFO - Retrieving MT5 data from 2025-07-15 19:45:00+00:00 to 2025-09-24 18:21:23.409034+00:00
2025-09-24 11:21:23,423 - INFO - Retrieved 14029 records from MT5
2025-09-24 11:21:23,423 - INFO - Parsing and standardizing datetime column
2025-09-24 11:21:23,432 - INFO - Date range: 2025-07-15 19:45:00+00:00 to 2025-09-24 18:20:00+00:00
2025-09-24 11:21:23,432 - INFO - Validating data quality
2025-09-24 11:21:23,438 - INFO - Filtering data to NY trading hours (09:30-17:00)
2025-09-24 11:21:23,554 - INFO - Filtered from 14029 to 4625 records (trading hours only)
2025-09-24 11:21:23,559 - INFO - Merging existing and new data
2025-09-24 11:21:23,702 - INFO - Removed 1 duplicate records during merge
2025-09-24 11:21:23,704 - INFO - Added 4625 new records from MT5
2025-09-24 11:21:23,704 - INFO - Generating summary report
2025-09-24 11:21:23,719 - INFO - Saving processed data to XAU_5m_data.csv
2025-09-24 11:21:27,652 - INFO - Successfully saved 484717 records to XAU_5m_data.csv
2025-09-24 11:21:27,663 - INFO - Pipeline completed successfully
2025-09-24 11:21:27,664 - INFO - Final dataset: 484717 records
2025-09-24 11:21:27,667 - INFO - Date range: 2004-06-11 13:30:00+00:00 to 2025-09-24 18:20:00+00:00
