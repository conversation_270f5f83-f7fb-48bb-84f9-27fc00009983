"""
LSTM Gold Trading Dataset Preparation Pipeline
==============================================

This module implements a comprehensive data preparation workflow for XAUUSD 5-minute data
to be used in LSTM-based gold trading strategy. It handles data validation, gap analysis,
MT5 integration, and quality control.

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
import MetaTrader5 as mt5
import pytz
from datetime import datetime, timedelta
import logging
import os
from typing import Tuple, Optional, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_preparation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class XAUDataPreparationPipeline:
    """
    Comprehensive data preparation pipeline for XAUUSD 5-minute data.
    Handles data validation, gap analysis, MT5 integration, and quality control.
    """
    
    def __init__(self, csv_file_path: str = "XAU_5m_data.csv"):
        """
        Initialize the data preparation pipeline.
        
        Args:
            csv_file_path: Path to the existing CSV file with XAUUSD data
        """
        self.csv_file_path = csv_file_path
        self.ny_tz = pytz.timezone('America/New_York')
        self.utc_tz = pytz.UTC
        self.mt5_symbol = "XAUUSD!"  # Correct symbol for MT5
        
        # Trading hours in NY time (09:30-17:00)
        self.trading_start_hour = 9
        self.trading_start_minute = 30
        self.trading_end_hour = 17
        self.trading_end_minute = 0
        
        # Data quality metrics
        self.quality_report = {
            'total_records': 0,
            'date_range': {'start': None, 'end': None},
            'gaps_found': 0,
            'invalid_records_removed': 0,
            'new_records_added': 0,
            'quality_issues': []
        }
    
    def load_existing_data(self) -> Optional[pd.DataFrame]:
        """
        Load existing CSV data and perform initial validation.
        
        Returns:
            DataFrame with existing data or None if file doesn't exist
        """
        logger.info(f"Loading existing data from {self.csv_file_path}")
        
        if not os.path.exists(self.csv_file_path):
            logger.warning(f"File {self.csv_file_path} not found. Will create new dataset.")
            return None
        
        try:
            # First, try to detect the CSV format
            with open(self.csv_file_path, 'r') as f:
                first_line = f.readline().strip()
                logger.info(f"First line of CSV: {first_line}")

            # Check if it's semicolon-separated
            if ';' in first_line and ',' not in first_line:
                logger.info("Detected semicolon-separated format")
                df = pd.read_csv(self.csv_file_path, sep=';')
            else:
                # Try standard comma-separated
                df = pd.read_csv(self.csv_file_path)

            logger.info(f"Successfully loaded {len(df)} records from CSV")

            # Display first few rows to understand structure
            logger.info("Data structure preview:")
            logger.info(f"Columns: {list(df.columns)}")
            logger.info(f"First 3 rows:\n{df.head(3)}")

            return df
            
        except Exception as e:
            logger.error(f"Error loading CSV file: {e}")
            return None
    
    def standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Standardize column names to match pipeline requirements.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with standardized columns
        """
        logger.info("Standardizing column names")
        
        # Common column name mappings
        column_mappings = {
            'time': 'datetime',
            'timestamp': 'datetime',
            'date': 'datetime',
            'Time': 'datetime',
            'Timestamp': 'datetime',
            'Date': 'datetime',
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'Vol': 'volume',
            'tick_volume': 'volume',
            'real_volume': 'volume'
        }

        # Handle special case where all data is in one column (semicolon-separated)
        if len(df.columns) == 1 and ';' in df.columns[0]:
            logger.info("Detected single column with semicolon-separated data")
            column_name = df.columns[0]

            # Split the column name to get individual column names
            if ';' in column_name:
                new_columns = column_name.split(';')
                logger.info(f"Splitting into columns: {new_columns}")

                # Split each row's data
                split_data = df[column_name].str.split(';', expand=True)
                split_data.columns = new_columns
                df = split_data

                logger.info(f"After splitting - Columns: {list(df.columns)}")
                logger.info(f"Sample data:\n{df.head(3)}")
        
        # Apply mappings
        df_renamed = df.rename(columns=column_mappings)
        
        # Ensure we have required columns
        required_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df_renamed.columns]
        
        if missing_columns:
            logger.error(f"Missing required columns: {missing_columns}")
            logger.info(f"Available columns: {list(df_renamed.columns)}")
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Select only required columns
        df_clean = df_renamed[required_columns].copy()
        
        logger.info(f"Standardized columns: {list(df_clean.columns)}")
        return df_clean
    
    def parse_datetime(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Parse and standardize datetime column with timezone handling.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with properly formatted datetime
        """
        logger.info("Parsing and standardizing datetime column")
        
        # Convert datetime column to pandas datetime
        df['datetime'] = pd.to_datetime(df['datetime'])
        
        # Handle timezone - assume UTC if no timezone info
        if df['datetime'].dt.tz is None:
            logger.info("No timezone info found, assuming UTC")
            df['datetime'] = df['datetime'].dt.tz_localize(self.utc_tz)
        
        # Convert to NY timezone for trading hours filtering
        df['datetime_ny'] = df['datetime'].dt.tz_convert(self.ny_tz)
        
        # Sort by datetime
        df = df.sort_values('datetime').reset_index(drop=True)
        
        logger.info(f"Date range: {df['datetime'].min()} to {df['datetime'].max()}")
        return df
    
    def validate_data_quality(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Validate data quality and remove invalid records.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with invalid records removed
        """
        logger.info("Validating data quality")
        initial_count = len(df)
        
        # Check for missing values
        missing_values = df.isnull().sum()
        if missing_values.any():
            logger.warning(f"Missing values found:\n{missing_values}")
            self.quality_report['quality_issues'].append(f"Missing values: {missing_values.to_dict()}")
        
        # Remove rows with missing critical values
        df_clean = df.dropna(subset=['datetime', 'open', 'high', 'low', 'close'])
        
        # Fill missing volume with 0 (common for some data sources)
        df_clean['volume'] = df_clean['volume'].fillna(0)
        
        # Validate OHLC relationships
        invalid_ohlc = (
            (df_clean['high'] < df_clean['low']) |
            (df_clean['close'] > df_clean['high']) |
            (df_clean['close'] < df_clean['low']) |
            (df_clean['open'] > df_clean['high']) |
            (df_clean['open'] < df_clean['low'])
        )
        
        if invalid_ohlc.any():
            invalid_count = invalid_ohlc.sum()
            logger.warning(f"Found {invalid_count} records with invalid OHLC relationships")
            self.quality_report['quality_issues'].append(f"Invalid OHLC relationships: {invalid_count}")
            df_clean = df_clean[~invalid_ohlc]
        
        # Remove zero volume bars (optional - depends on strategy requirements)
        zero_volume = df_clean['volume'] == 0
        if zero_volume.any():
            zero_count = zero_volume.sum()
            logger.info(f"Found {zero_count} records with zero volume")
            # Keep zero volume records but log them
            self.quality_report['quality_issues'].append(f"Zero volume records: {zero_count}")
        
        # Check for duplicate timestamps
        duplicates = df_clean.duplicated(subset=['datetime'])
        if duplicates.any():
            duplicate_count = duplicates.sum()
            logger.warning(f"Found {duplicate_count} duplicate timestamps")
            self.quality_report['quality_issues'].append(f"Duplicate timestamps: {duplicate_count}")
            df_clean = df_clean.drop_duplicates(subset=['datetime'])
        
        removed_count = initial_count - len(df_clean)
        self.quality_report['invalid_records_removed'] = removed_count
        
        if removed_count > 0:
            logger.info(f"Removed {removed_count} invalid records")
        
        return df_clean.reset_index(drop=True)

    def filter_trading_hours(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Filter data to only include NY trading hours (09:30-17:00).

        Args:
            df: Input DataFrame with datetime_ny column

        Returns:
            DataFrame filtered to trading hours only
        """
        logger.info("Filtering data to NY trading hours (09:30-17:00)")

        # Create time filter
        trading_hours_mask = (
            (df['datetime_ny'].dt.hour > self.trading_start_hour) |
            ((df['datetime_ny'].dt.hour == self.trading_start_hour) &
             (df['datetime_ny'].dt.minute >= self.trading_start_minute))
        ) & (
            (df['datetime_ny'].dt.hour < self.trading_end_hour) |
            ((df['datetime_ny'].dt.hour == self.trading_end_hour) &
             (df['datetime_ny'].dt.minute <= self.trading_end_minute))
        )

        # Filter weekdays only (Monday=0, Sunday=6)
        weekday_mask = df['datetime_ny'].dt.dayofweek < 5

        # Combine filters
        final_mask = trading_hours_mask & weekday_mask

        df_filtered = df[final_mask].copy()

        logger.info(f"Filtered from {len(df)} to {len(df_filtered)} records (trading hours only)")
        return df_filtered.reset_index(drop=True)

    def analyze_gaps(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze gaps in 5-minute intervals within the dataset.

        Args:
            df: Input DataFrame with datetime column

        Returns:
            Dictionary with gap analysis results
        """
        logger.info("Analyzing gaps in 5-minute intervals")

        if len(df) < 2:
            return {'gaps': [], 'total_gaps': 0, 'largest_gap_minutes': 0}

        # Calculate time differences
        df_sorted = df.sort_values('datetime')
        time_diffs = df_sorted['datetime'].diff()

        # Expected interval is 5 minutes
        expected_interval = timedelta(minutes=5)

        # Find gaps larger than expected (allowing for small tolerance)
        tolerance = timedelta(minutes=1)
        gap_mask = time_diffs > (expected_interval + tolerance)

        gaps = []
        if gap_mask.any():
            gap_indices = df_sorted[gap_mask].index
            for idx in gap_indices:
                prev_time = df_sorted.loc[idx-1, 'datetime']
                curr_time = df_sorted.loc[idx, 'datetime']
                gap_duration = curr_time - prev_time

                gaps.append({
                    'start_time': prev_time,
                    'end_time': curr_time,
                    'duration_minutes': gap_duration.total_seconds() / 60
                })

        total_gaps = len(gaps)
        largest_gap = max([g['duration_minutes'] for g in gaps]) if gaps else 0

        self.quality_report['gaps_found'] = total_gaps

        logger.info(f"Found {total_gaps} gaps in data")
        if gaps:
            logger.info(f"Largest gap: {largest_gap:.1f} minutes")

        return {
            'gaps': gaps,
            'total_gaps': total_gaps,
            'largest_gap_minutes': largest_gap
        }

    def connect_mt5(self) -> bool:
        """
        Connect to MetaTrader5 terminal.

        Returns:
            True if connection successful, False otherwise
        """
        logger.info("Connecting to MetaTrader5")

        if not mt5.initialize():
            logger.error(f"MT5 initialization failed: {mt5.last_error()}")
            return False

        # Check connection
        account_info = mt5.account_info()
        if account_info is None:
            logger.error("Failed to get account info from MT5")
            mt5.shutdown()
            return False

        logger.info(f"Connected to MT5 - Account: {account_info.login}")

        # Verify symbol exists
        symbol_info = mt5.symbol_info(self.mt5_symbol)
        if symbol_info is None:
            logger.error(f"Symbol {self.mt5_symbol} not found in MT5")
            mt5.shutdown()
            return False

        logger.info(f"Symbol {self.mt5_symbol} found - {symbol_info.description}")
        return True

    def get_mt5_data(self, start_date: datetime, end_date: datetime = None) -> Optional[pd.DataFrame]:
        """
        Retrieve XAUUSD data from MT5.

        Args:
            start_date: Start date for data retrieval
            end_date: End date for data retrieval (default: now)

        Returns:
            DataFrame with MT5 data or None if failed
        """
        if end_date is None:
            end_date = datetime.now(self.utc_tz)

        logger.info(f"Retrieving MT5 data from {start_date} to {end_date}")

        try:
            # Get rates from MT5
            rates = mt5.copy_rates_range(
                self.mt5_symbol,
                mt5.TIMEFRAME_M5,
                start_date,
                end_date
            )

            if rates is None or len(rates) == 0:
                logger.warning("No data retrieved from MT5")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(rates)

            # Convert time column to datetime
            df['datetime'] = pd.to_datetime(df['time'], unit='s', utc=True)

            # Rename columns to match our standard
            df = df.rename(columns={
                'tick_volume': 'volume'
            })

            # Select required columns
            df = df[['datetime', 'open', 'high', 'low', 'close', 'volume']].copy()

            logger.info(f"Retrieved {len(df)} records from MT5")
            return df

        except Exception as e:
            logger.error(f"Error retrieving MT5 data: {e}")
            return None

    def merge_and_deduplicate(self, existing_df: pd.DataFrame, new_df: pd.DataFrame) -> pd.DataFrame:
        """
        Merge existing and new data, removing duplicates.

        Args:
            existing_df: Existing DataFrame
            new_df: New DataFrame from MT5

        Returns:
            Merged DataFrame without duplicates
        """
        logger.info("Merging existing and new data")

        # Combine dataframes
        combined_df = pd.concat([existing_df, new_df], ignore_index=True)

        # Remove duplicates based on datetime
        initial_count = len(combined_df)
        combined_df = combined_df.drop_duplicates(subset=['datetime'])
        combined_df = combined_df.sort_values('datetime').reset_index(drop=True)

        final_count = len(combined_df)
        duplicates_removed = initial_count - final_count

        if duplicates_removed > 0:
            logger.info(f"Removed {duplicates_removed} duplicate records during merge")

        return combined_df

    def generate_summary_report(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate comprehensive summary report of the dataset.

        Args:
            df: Final processed DataFrame

        Returns:
            Dictionary with summary statistics
        """
        logger.info("Generating summary report")

        # Update quality report
        self.quality_report['total_records'] = len(df)
        if len(df) > 0:
            self.quality_report['date_range'] = {
                'start': df['datetime'].min(),
                'end': df['datetime'].max()
            }

        # Additional statistics
        report = {
            'dataset_summary': {
                'total_records': len(df),
                'date_range': {
                    'start': df['datetime'].min() if len(df) > 0 else None,
                    'end': df['datetime'].max() if len(df) > 0 else None,
                    'span_days': (df['datetime'].max() - df['datetime'].min()).days if len(df) > 0 else 0
                },
                'price_statistics': {
                    'min_price': df['low'].min() if len(df) > 0 else None,
                    'max_price': df['high'].max() if len(df) > 0 else None,
                    'avg_close': df['close'].mean() if len(df) > 0 else None
                },
                'volume_statistics': {
                    'total_volume': df['volume'].sum() if len(df) > 0 else 0,
                    'avg_volume': df['volume'].mean() if len(df) > 0 else 0,
                    'zero_volume_count': (df['volume'] == 0).sum() if len(df) > 0 else 0
                }
            },
            'quality_report': self.quality_report
        }

        return report

    def save_processed_data(self, df: pd.DataFrame, output_path: str = None) -> str:
        """
        Save processed data to CSV file.

        Args:
            df: Processed DataFrame
            output_path: Output file path (default: same as input)

        Returns:
            Path to saved file
        """
        if output_path is None:
            output_path = self.csv_file_path

        logger.info(f"Saving processed data to {output_path}")

        # Prepare final DataFrame for saving
        df_save = df.copy()

        # Convert datetime back to string for CSV compatibility
        df_save['datetime'] = df_save['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')

        # Remove temporary columns
        columns_to_remove = ['datetime_ny']
        df_save = df_save.drop(columns=[col for col in columns_to_remove if col in df_save.columns])

        # Save to CSV
        df_save.to_csv(output_path, index=False)

        logger.info(f"Successfully saved {len(df_save)} records to {output_path}")
        return output_path

    def run_pipeline(self) -> Dict[str, Any]:
        """
        Execute the complete data preparation pipeline.

        Returns:
            Summary report of the pipeline execution
        """
        logger.info("Starting XAUUSD data preparation pipeline")

        try:
            # Step 1: Load existing data
            existing_df = self.load_existing_data()

            if existing_df is None:
                logger.error("No existing data found. Please ensure XAU_5m_data.csv exists.")
                return {'success': False, 'error': 'No existing data found'}

            # Step 2: Standardize columns
            df = self.standardize_columns(existing_df)

            # Step 3: Parse datetime
            df = self.parse_datetime(df)

            # Step 4: Validate data quality
            df = self.validate_data_quality(df)

            # Step 5: Filter trading hours
            df = self.filter_trading_hours(df)

            # Step 6: Analyze gaps
            gap_analysis = self.analyze_gaps(df)

            # Step 7: Check if we need to update from MT5
            latest_timestamp = df['datetime'].max() if len(df) > 0 else None
            current_time = datetime.now(self.utc_tz)

            if latest_timestamp is None or (current_time - latest_timestamp).days > 1:
                logger.info("Data appears outdated, attempting to update from MT5")

                # Connect to MT5
                if self.connect_mt5():
                    try:
                        # Get new data from MT5
                        start_date = latest_timestamp if latest_timestamp else current_time - timedelta(days=30)
                        new_df = self.get_mt5_data(start_date, current_time)

                        if new_df is not None and len(new_df) > 0:
                            # Process new data
                            new_df = self.parse_datetime(new_df)
                            new_df = self.validate_data_quality(new_df)
                            new_df = self.filter_trading_hours(new_df)

                            # Merge with existing data
                            df = self.merge_and_deduplicate(df, new_df)
                            self.quality_report['new_records_added'] = len(new_df)

                            logger.info(f"Added {len(new_df)} new records from MT5")
                        else:
                            logger.info("No new data available from MT5")

                    finally:
                        mt5.shutdown()
                else:
                    logger.warning("Could not connect to MT5, using existing data only")
            else:
                logger.info("Data appears current, no MT5 update needed")

            # Step 8: Generate final report
            summary_report = self.generate_summary_report(df)

            # Step 9: Save processed data
            output_path = self.save_processed_data(df)

            # Log summary
            logger.info("Pipeline completed successfully")
            logger.info(f"Final dataset: {len(df)} records")
            logger.info(f"Date range: {df['datetime'].min()} to {df['datetime'].max()}")

            summary_report['success'] = True
            summary_report['output_file'] = output_path

            return summary_report

        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            return {'success': False, 'error': str(e)}


def print_summary_report(report: Dict[str, Any]) -> None:
    """
    Print a formatted summary report to console.

    Args:
        report: Summary report dictionary
    """
    print("\n" + "="*80)
    print("XAUUSD DATA PREPARATION PIPELINE - SUMMARY REPORT")
    print("="*80)

    if not report.get('success', False):
        print(f"❌ PIPELINE FAILED: {report.get('error', 'Unknown error')}")
        return

    print("✅ PIPELINE COMPLETED SUCCESSFULLY")
    print()

    # Dataset Summary
    dataset = report.get('dataset_summary', {})
    print("📊 DATASET SUMMARY:")
    print(f"   Total Records: {dataset.get('total_records', 0):,}")

    date_range = dataset.get('date_range', {})
    if date_range.get('start') and date_range.get('end'):
        print(f"   Date Range: {date_range['start']} to {date_range['end']}")
        print(f"   Span: {date_range.get('span_days', 0)} days")

    price_stats = dataset.get('price_statistics', {})
    if price_stats.get('min_price'):
        print(f"   Price Range: ${price_stats['min_price']:.2f} - ${price_stats['max_price']:.2f}")
        print(f"   Average Close: ${price_stats['avg_close']:.2f}")

    volume_stats = dataset.get('volume_statistics', {})
    print(f"   Total Volume: {volume_stats.get('total_volume', 0):,}")
    print(f"   Average Volume: {volume_stats.get('avg_volume', 0):.0f}")
    print(f"   Zero Volume Records: {volume_stats.get('zero_volume_count', 0)}")
    print()

    # Quality Report
    quality = report.get('quality_report', {})
    print("🔍 DATA QUALITY REPORT:")
    print(f"   Gaps Found: {quality.get('gaps_found', 0)}")
    print(f"   Invalid Records Removed: {quality.get('invalid_records_removed', 0)}")
    print(f"   New Records Added: {quality.get('new_records_added', 0)}")

    issues = quality.get('quality_issues', [])
    if issues:
        print("   Quality Issues:")
        for issue in issues:
            print(f"     - {issue}")
    else:
        print("   ✅ No quality issues found")

    print()
    print(f"📁 Output File: {report.get('output_file', 'N/A')}")
    print("="*80)


def main():
    """
    Main execution function for the data preparation pipeline.
    """
    print("XAUUSD Data Preparation Pipeline")
    print("================================")

    # Initialize pipeline
    pipeline = XAUDataPreparationPipeline("XAU_5m_data.csv")

    # Run pipeline
    report = pipeline.run_pipeline()

    # Print summary report
    print_summary_report(report)

    # Save detailed report to file
    import json
    with open('data_preparation_report.json', 'w') as f:
        # Convert datetime objects to strings for JSON serialization
        json_report = report.copy()
        if 'dataset_summary' in json_report and 'date_range' in json_report['dataset_summary']:
            date_range = json_report['dataset_summary']['date_range']
            if date_range.get('start'):
                date_range['start'] = str(date_range['start'])
            if date_range.get('end'):
                date_range['end'] = str(date_range['end'])

        json.dump(json_report, f, indent=2, default=str)

    print(f"\n📄 Detailed report saved to: data_preparation_report.json")

    return report


if __name__ == "__main__":
    main()
