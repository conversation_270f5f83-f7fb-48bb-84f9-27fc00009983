"""
Data Normalization and Preprocessing Module for LSTM Gold Trading Strategy
==========================================================================

This module handles advanced data preprocessing including:
- Feature normalization and scaling
- Outlier detection and handling
- Missing value imputation
- Data quality validation
- Feature engineering enhancements

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from sklearn.impute import SimpleImputer, KNNImputer
from scipy import stats
import logging
from typing import Dict, Tuple, Optional, Any, List
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class DataPreprocessor:
    """
    Advanced data preprocessing for LSTM trading strategy.
    Handles normalization, outlier detection, and missing value imputation.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize data preprocessor.
        
        Args:
            config: Configuration dictionary with preprocessing parameters
        """
        # Default configuration
        self.config = config or {
            'normalization': {
                'method': 'minmax',  # 'minmax', 'standard', 'robust'
                'feature_range': (-1, 1),
                'clip_outliers': True,
                'outlier_std_threshold': 3.0
            },
            'missing_values': {
                'strategy': 'interpolate',  # 'drop', 'interpolate', 'knn', 'forward_fill'
                'max_missing_ratio': 0.05,  # Maximum 5% missing values allowed
                'interpolation_method': 'linear'
            },
            'outlier_detection': {
                'method': 'iqr',  # 'iqr', 'zscore', 'isolation_forest'
                'iqr_multiplier': 1.5,
                'zscore_threshold': 3.0,
                'handle_outliers': 'clip'  # 'clip', 'remove', 'winsorize'
            },
            'feature_engineering': {
                'add_rolling_stats': True,
                'rolling_windows': [5, 10, 20],
                'add_lag_features': False,
                'lag_periods': [1, 2, 3]
            }
        }
        
        # Initialize scalers and imputers
        self.scalers = {}
        self.imputers = {}
        self.outlier_bounds = {}
        self.is_fitted = False
        
        logger.info("Data preprocessor initialized")
    
    def detect_outliers_iqr(self, series: pd.Series, multiplier: float = 1.5) -> Tuple[pd.Series, Dict]:
        """
        Detect outliers using Interquartile Range (IQR) method.
        
        Args:
            series: Input series
            multiplier: IQR multiplier for outlier detection
            
        Returns:
            Tuple of (outlier_mask, bounds_dict)
        """
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - multiplier * IQR
        upper_bound = Q3 + multiplier * IQR
        
        outlier_mask = (series < lower_bound) | (series > upper_bound)
        bounds = {'lower': lower_bound, 'upper': upper_bound, 'Q1': Q1, 'Q3': Q3, 'IQR': IQR}
        
        return outlier_mask, bounds
    
    def detect_outliers_zscore(self, series: pd.Series, threshold: float = 3.0) -> Tuple[pd.Series, Dict]:
        """
        Detect outliers using Z-score method.
        
        Args:
            series: Input series
            threshold: Z-score threshold for outlier detection
            
        Returns:
            Tuple of (outlier_mask, bounds_dict)
        """
        z_scores = np.abs(stats.zscore(series.dropna()))
        mean_val = series.mean()
        std_val = series.std()
        
        lower_bound = mean_val - threshold * std_val
        upper_bound = mean_val + threshold * std_val
        
        outlier_mask = (series < lower_bound) | (series > upper_bound)
        bounds = {'lower': lower_bound, 'upper': upper_bound, 'mean': mean_val, 'std': std_val}
        
        return outlier_mask, bounds
    
    def handle_outliers(self, series: pd.Series, method: str = 'clip') -> pd.Series:
        """
        Handle outliers in the series.
        
        Args:
            series: Input series
            method: Method to handle outliers ('clip', 'remove', 'winsorize')
            
        Returns:
            Series with outliers handled
        """
        outlier_config = self.config['outlier_detection']
        detection_method = outlier_config['method']
        
        if detection_method == 'iqr':
            outlier_mask, bounds = self.detect_outliers_iqr(
                series, outlier_config['iqr_multiplier']
            )
        elif detection_method == 'zscore':
            outlier_mask, bounds = self.detect_outliers_zscore(
                series, outlier_config['zscore_threshold']
            )
        else:
            logger.warning(f"Unknown outlier detection method: {detection_method}")
            return series
        
        # Store bounds for later use
        self.outlier_bounds[series.name] = bounds
        
        outlier_count = outlier_mask.sum()
        if outlier_count > 0:
            logger.info(f"Detected {outlier_count} outliers in {series.name}")
            
            if method == 'clip':
                # Clip outliers to bounds
                result = series.clip(lower=bounds['lower'], upper=bounds['upper'])
            elif method == 'remove':
                # Set outliers to NaN (will be handled by missing value strategy)
                result = series.copy()
                result[outlier_mask] = np.nan
            elif method == 'winsorize':
                # Winsorize at 5th and 95th percentiles
                result = series.copy()
                lower_percentile = series.quantile(0.05)
                upper_percentile = series.quantile(0.95)
                result = result.clip(lower=lower_percentile, upper=upper_percentile)
            else:
                logger.warning(f"Unknown outlier handling method: {method}")
                result = series
        else:
            result = series
        
        return result
    
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Handle missing values in the DataFrame.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with missing values handled
        """
        missing_config = self.config['missing_values']
        strategy = missing_config['strategy']
        
        logger.info(f"Handling missing values using strategy: {strategy}")
        
        # Check missing value ratios
        missing_ratios = df.isnull().sum() / len(df)
        high_missing_cols = missing_ratios[missing_ratios > missing_config['max_missing_ratio']]
        
        if len(high_missing_cols) > 0:
            logger.warning(f"Columns with high missing ratios: {high_missing_cols.to_dict()}")
        
        result_df = df.copy()
        
        if strategy == 'drop':
            # Drop rows with any missing values
            result_df = result_df.dropna()
            logger.info(f"Dropped {len(df) - len(result_df)} rows with missing values")
        
        elif strategy == 'interpolate':
            # Interpolate missing values
            method = missing_config['interpolation_method']
            result_df = result_df.interpolate(method=method)
            
            # Forward fill any remaining NaN at the beginning
            result_df = result_df.fillna(method='ffill')
            
            # Backward fill any remaining NaN at the end
            result_df = result_df.fillna(method='bfill')
            
        elif strategy == 'knn':
            # Use KNN imputation
            imputer = KNNImputer(n_neighbors=5)
            numeric_cols = result_df.select_dtypes(include=[np.number]).columns
            
            result_df[numeric_cols] = imputer.fit_transform(result_df[numeric_cols])
            self.imputers['knn'] = imputer
            
        elif strategy == 'forward_fill':
            # Forward fill missing values
            result_df = result_df.fillna(method='ffill')
            result_df = result_df.fillna(method='bfill')  # Handle initial NaNs
        
        else:
            logger.warning(f"Unknown missing value strategy: {strategy}")
        
        # Log results
        remaining_missing = result_df.isnull().sum().sum()
        logger.info(f"Missing values after handling: {remaining_missing}")
        
        return result_df
    
    def normalize_features(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """
        Normalize feature columns.
        
        Args:
            df: Input DataFrame
            feature_columns: List of columns to normalize
            
        Returns:
            DataFrame with normalized features
        """
        norm_config = self.config['normalization']
        method = norm_config['method']
        
        logger.info(f"Normalizing features using method: {method}")
        
        result_df = df.copy()
        
        for col in feature_columns:
            if col not in df.columns:
                logger.warning(f"Column {col} not found in DataFrame")
                continue
            
            # Handle outliers first if configured
            if norm_config.get('clip_outliers', False):
                result_df[col] = self.handle_outliers(
                    result_df[col], 
                    self.config['outlier_detection']['handle_outliers']
                )
            
            # Initialize scaler
            if method == 'minmax':
                scaler = MinMaxScaler(feature_range=tuple(norm_config['feature_range']))
            elif method == 'standard':
                scaler = StandardScaler()
            elif method == 'robust':
                scaler = RobustScaler()
            else:
                logger.error(f"Unknown normalization method: {method}")
                continue
            
            # Fit and transform
            valid_data = result_df[col].dropna()
            if len(valid_data) > 0:
                scaler.fit(valid_data.values.reshape(-1, 1))
                self.scalers[col] = scaler
                
                # Transform all data (including NaN)
                valid_mask = ~result_df[col].isna()
                if valid_mask.any():
                    result_df.loc[valid_mask, col] = scaler.transform(
                        result_df.loc[valid_mask, col].values.reshape(-1, 1)
                    ).flatten()
                
                logger.info(f"Normalized {col}: range [{result_df[col].min():.3f}, {result_df[col].max():.3f}]")
            else:
                logger.warning(f"No valid data for normalization of {col}")
        
        self.is_fitted = True
        return result_df

    def add_rolling_statistics(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """
        Add rolling statistics as additional features.

        Args:
            df: Input DataFrame
            feature_columns: List of base feature columns

        Returns:
            DataFrame with additional rolling statistics features
        """
        if not self.config['feature_engineering']['add_rolling_stats']:
            return df

        logger.info("Adding rolling statistics features")

        result_df = df.copy()
        windows = self.config['feature_engineering']['rolling_windows']

        for col in feature_columns:
            if col not in df.columns:
                continue

            for window in windows:
                # Rolling mean
                result_df[f'{col}_ma_{window}'] = df[col].rolling(window=window).mean()

                # Rolling standard deviation
                result_df[f'{col}_std_{window}'] = df[col].rolling(window=window).std()

                # Rolling min/max
                result_df[f'{col}_min_{window}'] = df[col].rolling(window=window).min()
                result_df[f'{col}_max_{window}'] = df[col].rolling(window=window).max()

        logger.info(f"Added rolling statistics for {len(feature_columns)} features")
        return result_df

    def add_lag_features(self, df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
        """
        Add lagged features.

        Args:
            df: Input DataFrame
            feature_columns: List of base feature columns

        Returns:
            DataFrame with additional lag features
        """
        if not self.config['feature_engineering']['add_lag_features']:
            return df

        logger.info("Adding lag features")

        result_df = df.copy()
        lag_periods = self.config['feature_engineering']['lag_periods']

        for col in feature_columns:
            if col not in df.columns:
                continue

            for lag in lag_periods:
                result_df[f'{col}_lag_{lag}'] = df[col].shift(lag)

        logger.info(f"Added lag features for {len(feature_columns)} features")
        return result_df

    def validate_processed_data(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """
        Validate processed data quality.

        Args:
            df: Processed DataFrame
            feature_columns: List of feature columns to validate

        Returns:
            Dictionary with validation results
        """
        logger.info("Validating processed data")

        validation_report = {
            'total_records': len(df),
            'feature_validation': {},
            'overall_quality': 'good',
            'issues': [],
            'recommendations': []
        }

        for col in feature_columns:
            if col not in df.columns:
                validation_report['issues'].append(f"Missing feature column: {col}")
                continue

            series = df[col]

            # Basic statistics
            stats = {
                'count': series.notna().sum(),
                'missing': series.isna().sum(),
                'missing_ratio': series.isna().sum() / len(series),
                'min': series.min(),
                'max': series.max(),
                'mean': series.mean(),
                'std': series.std(),
                'skewness': series.skew(),
                'kurtosis': series.kurtosis()
            }

            # Check for issues
            if stats['missing_ratio'] > 0.05:
                validation_report['issues'].append(
                    f"{col}: High missing ratio ({stats['missing_ratio']:.1%})"
                )

            if abs(stats['skewness']) > 2:
                validation_report['recommendations'].append(
                    f"{col}: High skewness ({stats['skewness']:.2f}), consider transformation"
                )

            if abs(stats['kurtosis']) > 7:
                validation_report['recommendations'].append(
                    f"{col}: High kurtosis ({stats['kurtosis']:.2f}), check for outliers"
                )

            # Check normalization
            if col.endswith('_norm'):
                if stats['min'] < -1.1 or stats['max'] > 1.1:
                    validation_report['issues'].append(
                        f"{col}: Values outside expected range [-1, 1]"
                    )

            validation_report['feature_validation'][col] = stats

        # Overall assessment
        if len(validation_report['issues']) > 0:
            validation_report['overall_quality'] = 'issues_found'
        elif len(validation_report['recommendations']) > 3:
            validation_report['overall_quality'] = 'needs_attention'

        logger.info(f"Validation completed: {validation_report['overall_quality']}")
        return validation_report

    def process_features(self, df: pd.DataFrame, feature_columns: List[str]) -> Tuple[pd.DataFrame, Dict]:
        """
        Complete feature processing pipeline.

        Args:
            df: Input DataFrame with calculated indicators
            feature_columns: List of feature columns to process

        Returns:
            Tuple of (processed_dataframe, processing_report)
        """
        logger.info("Starting complete feature processing pipeline")

        processing_report = {
            'steps_completed': [],
            'original_shape': df.shape,
            'processing_stats': {}
        }

        # Step 1: Handle missing values
        logger.info("Step 1: Handling missing values")
        df_processed = self.handle_missing_values(df)
        processing_report['steps_completed'].append('missing_values_handled')
        processing_report['processing_stats']['missing_values'] = {
            'original_missing': df.isnull().sum().sum(),
            'final_missing': df_processed.isnull().sum().sum()
        }

        # Step 2: Normalize features
        logger.info("Step 2: Normalizing features")
        df_processed = self.normalize_features(df_processed, feature_columns)
        processing_report['steps_completed'].append('features_normalized')

        # Step 3: Add rolling statistics (if configured)
        if self.config['feature_engineering']['add_rolling_stats']:
            logger.info("Step 3: Adding rolling statistics")
            df_processed = self.add_rolling_statistics(df_processed, feature_columns)
            processing_report['steps_completed'].append('rolling_stats_added')

        # Step 4: Add lag features (if configured)
        if self.config['feature_engineering']['add_lag_features']:
            logger.info("Step 4: Adding lag features")
            df_processed = self.add_lag_features(df_processed, feature_columns)
            processing_report['steps_completed'].append('lag_features_added')

        # Step 5: Final validation
        logger.info("Step 5: Final validation")
        validation_report = self.validate_processed_data(df_processed, feature_columns)
        processing_report['validation_report'] = validation_report
        processing_report['steps_completed'].append('validation_completed')

        # Final statistics
        processing_report['final_shape'] = df_processed.shape
        processing_report['shape_change'] = {
            'rows_change': df_processed.shape[0] - df.shape[0],
            'cols_change': df_processed.shape[1] - df.shape[1]
        }

        logger.info(f"Feature processing completed: {df.shape} -> {df_processed.shape}")
        return df_processed, processing_report

    def save_preprocessing_objects(self, filepath: str) -> None:
        """
        Save preprocessing objects (scalers, imputers, etc.) to file.

        Args:
            filepath: Path to save objects
        """
        import pickle

        if not self.is_fitted:
            logger.warning("Preprocessing objects not fitted yet, nothing to save")
            return

        preprocessing_objects = {
            'scalers': self.scalers,
            'imputers': self.imputers,
            'outlier_bounds': self.outlier_bounds,
            'config': self.config
        }

        with open(filepath, 'wb') as f:
            pickle.dump(preprocessing_objects, f)

        logger.info(f"Preprocessing objects saved to {filepath}")

    def load_preprocessing_objects(self, filepath: str) -> None:
        """
        Load preprocessing objects from file.

        Args:
            filepath: Path to load objects from
        """
        import pickle

        with open(filepath, 'rb') as f:
            preprocessing_objects = pickle.load(f)

        self.scalers = preprocessing_objects['scalers']
        self.imputers = preprocessing_objects['imputers']
        self.outlier_bounds = preprocessing_objects['outlier_bounds']
        self.config = preprocessing_objects['config']
        self.is_fitted = True

        logger.info(f"Preprocessing objects loaded from {filepath}")

    def get_feature_importance_stats(self, df: pd.DataFrame, feature_columns: List[str]) -> Dict[str, Any]:
        """
        Calculate feature importance statistics.

        Args:
            df: DataFrame with features
            feature_columns: List of feature columns

        Returns:
            Dictionary with feature importance statistics
        """
        logger.info("Calculating feature importance statistics")

        stats = {}

        for col in feature_columns:
            if col not in df.columns:
                continue

            series = df[col]

            # Calculate various statistics that might indicate importance
            stats[col] = {
                'variance': series.var(),
                'range': series.max() - series.min(),
                'coefficient_of_variation': series.std() / abs(series.mean()) if series.mean() != 0 else np.inf,
                'information_ratio': abs(series.mean()) / series.std() if series.std() != 0 else 0,
                'completeness': series.notna().sum() / len(series)
            }

        return stats
