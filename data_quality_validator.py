"""
Data Quality Controls and Validation Module for LSTM Gold Trading Strategy
==========================================================================

This module implements comprehensive data quality checks including:
- Trading hours validation
- Volume and price movement analysis
- Data completeness and consistency checks
- Market gap detection and handling
- Statistical anomaly detection
- LSTM training readiness validation

Key Features:
- Comprehensive quality scoring
- Detailed reporting and recommendations
- Automated data cleaning suggestions
- Performance metrics validation
- Training dataset readiness assessment

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from datetime import datetime, timedelta
import warnings
from scipy import stats
import json

warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class DataQualityValidator:
    """
    Comprehensive data quality validation for LSTM trading strategy.
    Implements multiple validation layers and quality scoring.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize data quality validator.
        
        Args:
            config: Configuration dictionary with validation parameters
        """
        # Default configuration
        self.config = config or {
            'trading_hours': {
                'start_hour': 9,
                'start_minute': 30,
                'end_hour': 17,
                'end_minute': 0,
                'timezone': 'America/New_York'
            },
            'volume_checks': {
                'min_volume_threshold': 0,  # Minimum volume per bar
                'zero_volume_tolerance': 0.05,  # Max 5% zero volume bars
                'volume_spike_threshold': 10.0  # 10x average volume
            },
            'price_checks': {
                'max_price_change_pct': 5.0,  # Max 5% price change per bar
                'min_price_movement': 0.01,  # Minimum meaningful price movement
                'ohlc_consistency_tolerance': 0.001,  # OHLC relationship tolerance
                'price_spike_threshold': 3.0  # 3 standard deviations
            },
            'data_completeness': {
                'min_samples_required': 1000,  # Minimum samples for training
                'max_missing_ratio': 0.01,  # Max 1% missing data
                'min_date_coverage_days': 30,  # Minimum date coverage
                'required_features': ['open', 'high', 'low', 'close', 'volume']
            },
            'sequence_validation': {
                'min_sequences_required': 1000,  # Minimum sequences for LSTM
                'label_balance_threshold': 0.05,  # Min 5% for each class
                'feature_correlation_threshold': 0.95  # Max correlation between features
            },
            'quality_thresholds': {
                'excellent': 0.95,
                'good': 0.85,
                'acceptable': 0.70,
                'poor': 0.50
            }
        }
        
        # Validation results storage
        self.validation_results = {}
        self.quality_score = 0.0
        self.recommendations = []
        self.critical_issues = []
        
        logger.info("Data quality validator initialized")
    
    def validate_trading_hours(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate that data covers proper trading hours.
        
        Args:
            df: DataFrame with datetime index
            
        Returns:
            Dictionary with trading hours validation results
        """
        logger.info("Validating trading hours coverage")
        
        if not isinstance(df.index, pd.DatetimeIndex):
            return {
                'status': 'error',
                'message': 'DataFrame must have DatetimeIndex',
                'score': 0.0
            }
        
        # Convert to NY timezone
        df_ny = df.copy()
        if df_ny.index.tz is None:
            df_ny.index = df_ny.index.tz_localize('UTC')
        df_ny.index = df_ny.index.tz_convert('America/New_York')
        
        # Extract hour and minute
        hours = df_ny.index.hour
        minutes = df_ny.index.minute
        
        # Define trading hours
        start_time = self.config['trading_hours']['start_hour'] * 60 + self.config['trading_hours']['start_minute']
        end_time = self.config['trading_hours']['end_hour'] * 60 + self.config['trading_hours']['end_minute']
        
        # Check if data falls within trading hours
        data_minutes = hours * 60 + minutes
        within_hours = (data_minutes >= start_time) & (data_minutes <= end_time)
        
        # Check weekdays only
        weekdays = df_ny.index.weekday < 5
        
        # Combined check
        valid_trading_time = within_hours & weekdays
        
        coverage_ratio = valid_trading_time.sum() / len(df_ny)
        
        result = {
            'total_records': len(df_ny),
            'trading_hours_records': valid_trading_time.sum(),
            'coverage_ratio': coverage_ratio,
            'outside_hours_records': (~valid_trading_time).sum(),
            'weekend_records': (~weekdays).sum(),
            'score': min(1.0, coverage_ratio * 1.2)  # Slight bonus for good coverage
        }
        
        if coverage_ratio < 0.8:
            result['status'] = 'warning'
            result['message'] = f'Low trading hours coverage: {coverage_ratio:.1%}'
        else:
            result['status'] = 'good'
            result['message'] = f'Good trading hours coverage: {coverage_ratio:.1%}'
        
        return result
    
    def validate_volume_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate volume data quality.
        
        Args:
            df: DataFrame with volume column
            
        Returns:
            Dictionary with volume validation results
        """
        logger.info("Validating volume data")
        
        if 'volume' not in df.columns:
            return {
                'status': 'error',
                'message': 'Volume column not found',
                'score': 0.0
            }
        
        volume = df['volume']
        
        # Basic statistics
        zero_volume_count = (volume == 0).sum()
        zero_volume_ratio = zero_volume_count / len(volume)
        
        # Volume spikes detection
        volume_mean = volume.mean()
        volume_std = volume.std()
        spike_threshold = volume_mean + self.config['volume_checks']['volume_spike_threshold'] * volume_std
        volume_spikes = (volume > spike_threshold).sum()
        
        # Negative volume check
        negative_volume = (volume < 0).sum()
        
        result = {
            'total_records': len(volume),
            'zero_volume_count': zero_volume_count,
            'zero_volume_ratio': zero_volume_ratio,
            'negative_volume_count': negative_volume,
            'volume_spikes': volume_spikes,
            'mean_volume': volume_mean,
            'std_volume': volume_std,
            'min_volume': volume.min(),
            'max_volume': volume.max()
        }
        
        # Calculate score
        score = 1.0
        
        if zero_volume_ratio > self.config['volume_checks']['zero_volume_tolerance']:
            score -= 0.3
            result['status'] = 'warning'
            result['message'] = f'High zero volume ratio: {zero_volume_ratio:.1%}'
        
        if negative_volume > 0:
            score -= 0.5
            result['status'] = 'error'
            result['message'] = f'Found {negative_volume} negative volume records'
        
        if volume_spikes > len(volume) * 0.01:  # More than 1% spikes
            score -= 0.2
            if 'message' not in result:
                result['message'] = f'High volume spike count: {volume_spikes}'
        
        if 'status' not in result:
            result['status'] = 'good'
            result['message'] = 'Volume data quality is good'
        
        result['score'] = max(0.0, score)
        return result
    
    def validate_price_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate OHLC price data quality.
        
        Args:
            df: DataFrame with OHLC columns
            
        Returns:
            Dictionary with price validation results
        """
        logger.info("Validating price data")
        
        required_cols = ['open', 'high', 'low', 'close']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            return {
                'status': 'error',
                'message': f'Missing price columns: {missing_cols}',
                'score': 0.0
            }
        
        # OHLC consistency checks
        ohlc_issues = 0
        
        # High should be >= Open, Low, Close
        high_issues = ((df['high'] < df['open']) | 
                      (df['high'] < df['low']) | 
                      (df['high'] < df['close'])).sum()
        
        # Low should be <= Open, High, Close
        low_issues = ((df['low'] > df['open']) | 
                     (df['low'] > df['high']) | 
                     (df['low'] > df['close'])).sum()
        
        ohlc_issues = high_issues + low_issues
        
        # Price spike detection
        close_returns = df['close'].pct_change().abs()
        spike_threshold = self.config['price_checks']['max_price_change_pct'] / 100
        price_spikes = (close_returns > spike_threshold).sum()
        
        # Zero price movement detection
        zero_movement = ((df['high'] == df['low']) & 
                        (df['open'] == df['close'])).sum()
        
        # Missing values
        missing_values = df[required_cols].isnull().sum().sum()
        
        result = {
            'total_records': len(df),
            'ohlc_consistency_issues': ohlc_issues,
            'high_issues': high_issues,
            'low_issues': low_issues,
            'price_spikes': price_spikes,
            'zero_movement_bars': zero_movement,
            'missing_values': missing_values,
            'price_statistics': {
                'mean_close': df['close'].mean(),
                'std_close': df['close'].std(),
                'min_close': df['close'].min(),
                'max_close': df['close'].max(),
                'mean_range': (df['high'] - df['low']).mean()
            }
        }
        
        # Calculate score
        score = 1.0
        
        if ohlc_issues > 0:
            score -= 0.4
            result['status'] = 'error'
            result['message'] = f'OHLC consistency issues: {ohlc_issues}'
        
        if price_spikes > len(df) * 0.001:  # More than 0.1% spikes
            score -= 0.2
            if 'message' not in result:
                result['message'] = f'High price spike count: {price_spikes}'
        
        if missing_values > 0:
            score -= 0.3
            if 'message' not in result:
                result['message'] = f'Missing price values: {missing_values}'
        
        if 'status' not in result:
            result['status'] = 'good'
            result['message'] = 'Price data quality is good'
        
        result['score'] = max(0.0, score)
        return result
    
    def validate_data_completeness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate data completeness and coverage.
        
        Args:
            df: DataFrame to validate
            
        Returns:
            Dictionary with completeness validation results
        """
        logger.info("Validating data completeness")
        
        # Basic completeness metrics
        total_records = len(df)
        date_range = df.index.max() - df.index.min()
        date_coverage_days = date_range.days
        
        # Missing data analysis
        missing_data = df.isnull().sum()
        total_missing = missing_data.sum()
        missing_ratio = total_missing / (len(df) * len(df.columns))
        
        # Required features check
        required_features = self.config['data_completeness']['required_features']
        missing_features = [col for col in required_features if col not in df.columns]
        
        # Data density (records per day)
        expected_records_per_day = 24 * 60 / 5  # 5-minute bars
        actual_density = total_records / max(1, date_coverage_days)
        density_ratio = actual_density / expected_records_per_day
        
        result = {
            'total_records': total_records,
            'date_coverage_days': date_coverage_days,
            'missing_ratio': missing_ratio,
            'missing_features': missing_features,
            'data_density': actual_density,
            'expected_density': expected_records_per_day,
            'density_ratio': density_ratio,
            'missing_by_column': missing_data.to_dict()
        }
        
        # Calculate score
        score = 1.0
        
        if total_records < self.config['data_completeness']['min_samples_required']:
            score -= 0.5
            result['status'] = 'error'
            result['message'] = f'Insufficient data: {total_records} records'
        
        if missing_ratio > self.config['data_completeness']['max_missing_ratio']:
            score -= 0.3
            if 'message' not in result:
                result['message'] = f'High missing data ratio: {missing_ratio:.1%}'
        
        if len(missing_features) > 0:
            score -= 0.4
            if 'message' not in result:
                result['message'] = f'Missing required features: {missing_features}'
        
        if date_coverage_days < self.config['data_completeness']['min_date_coverage_days']:
            score -= 0.2
            if 'message' not in result:
                result['message'] = f'Insufficient date coverage: {date_coverage_days} days'
        
        if 'status' not in result:
            result['status'] = 'good'
            result['message'] = 'Data completeness is good'
        
        result['score'] = max(0.0, score)
        return result

    def validate_sequence_quality(self, sequences: np.ndarray, labels: np.ndarray) -> Dict[str, Any]:
        """
        Validate LSTM sequence and label quality.

        Args:
            sequences: LSTM sequences array
            labels: Labels array

        Returns:
            Dictionary with sequence validation results
        """
        logger.info("Validating LSTM sequence quality")

        if len(sequences) == 0:
            return {
                'status': 'error',
                'message': 'No sequences provided',
                'score': 0.0
            }

        # Basic sequence statistics
        total_sequences = len(sequences)
        sequence_shape = sequences.shape

        # Data quality checks
        nan_count = np.isnan(sequences).sum()
        inf_count = np.isinf(sequences).sum()
        finite_ratio = np.isfinite(sequences).mean()

        # Feature correlation analysis
        if len(sequences) > 0 and sequences.shape[2] > 1:
            # Flatten sequences for correlation analysis
            flattened = sequences.reshape(-1, sequences.shape[2])
            correlation_matrix = np.corrcoef(flattened.T)
            max_correlation = np.max(np.abs(correlation_matrix - np.eye(correlation_matrix.shape[0])))
        else:
            max_correlation = 0.0

        # Label distribution analysis
        if isinstance(labels[0], str):
            unique_labels, label_counts = np.unique(labels, return_counts=True)
            label_distribution = dict(zip(unique_labels, label_counts))
            min_class_ratio = min(label_counts) / len(labels)
        else:
            # One-hot encoded labels
            label_sums = labels.sum(axis=0)
            label_distribution = {f'class_{i}': int(count) for i, count in enumerate(label_sums)}
            min_class_ratio = min(label_sums) / len(labels)

        result = {
            'total_sequences': total_sequences,
            'sequence_shape': sequence_shape,
            'nan_count': int(nan_count),
            'inf_count': int(inf_count),
            'finite_ratio': float(finite_ratio),
            'max_feature_correlation': float(max_correlation),
            'label_distribution': label_distribution,
            'min_class_ratio': float(min_class_ratio),
            'value_statistics': {
                'mean': float(np.mean(sequences)),
                'std': float(np.std(sequences)),
                'min': float(np.min(sequences)),
                'max': float(np.max(sequences))
            }
        }

        # Calculate score
        score = 1.0

        if total_sequences < self.config['sequence_validation']['min_sequences_required']:
            score -= 0.4
            result['status'] = 'warning'
            result['message'] = f'Low sequence count: {total_sequences}'

        if finite_ratio < 0.99:
            score -= 0.3
            if 'message' not in result:
                result['message'] = f'Data quality issues: {finite_ratio:.1%} finite values'

        if max_correlation > self.config['sequence_validation']['feature_correlation_threshold']:
            score -= 0.2
            if 'message' not in result:
                result['message'] = f'High feature correlation: {max_correlation:.3f}'

        if min_class_ratio < self.config['sequence_validation']['label_balance_threshold']:
            score -= 0.3
            if 'message' not in result:
                result['message'] = f'Imbalanced labels: {min_class_ratio:.1%} min class'

        if 'status' not in result:
            result['status'] = 'good'
            result['message'] = 'Sequence quality is good'

        result['score'] = max(0.0, score)
        return result

    def run_comprehensive_validation(self, df: pd.DataFrame, sequences: np.ndarray = None,
                                   labels: np.ndarray = None) -> Dict[str, Any]:
        """
        Run comprehensive data quality validation.

        Args:
            df: Main DataFrame with OHLC data
            sequences: Optional LSTM sequences
            labels: Optional labels

        Returns:
            Comprehensive validation report
        """
        logger.info("Running comprehensive data quality validation")

        validation_results = {}

        # Run individual validations
        validation_results['trading_hours'] = self.validate_trading_hours(df)
        validation_results['volume_data'] = self.validate_volume_data(df)
        validation_results['price_data'] = self.validate_price_data(df)
        validation_results['data_completeness'] = self.validate_data_completeness(df)

        if sequences is not None and labels is not None:
            validation_results['sequence_quality'] = self.validate_sequence_quality(sequences, labels)

        # Calculate overall quality score
        scores = [result['score'] for result in validation_results.values()]
        overall_score = np.mean(scores)

        # Determine quality level
        thresholds = self.config['quality_thresholds']
        if overall_score >= thresholds['excellent']:
            quality_level = 'excellent'
        elif overall_score >= thresholds['good']:
            quality_level = 'good'
        elif overall_score >= thresholds['acceptable']:
            quality_level = 'acceptable'
        elif overall_score >= thresholds['poor']:
            quality_level = 'poor'
        else:
            quality_level = 'critical'

        # Collect issues and recommendations
        issues = []
        recommendations = []

        for validation_name, result in validation_results.items():
            if result.get('status') == 'error':
                issues.append(f"{validation_name}: {result.get('message', 'Unknown error')}")
            elif result.get('status') == 'warning':
                recommendations.append(f"{validation_name}: {result.get('message', 'Unknown warning')}")

        # Generate recommendations based on issues
        if len(issues) > 0:
            recommendations.extend(self._generate_recommendations(validation_results))

        # Compile comprehensive report
        comprehensive_report = {
            'validation_timestamp': datetime.now().isoformat(),
            'overall_quality_score': overall_score,
            'quality_level': quality_level,
            'individual_validations': validation_results,
            'critical_issues': issues,
            'recommendations': recommendations,
            'summary': {
                'total_validations': len(validation_results),
                'passed_validations': sum(1 for r in validation_results.values() if r.get('status') == 'good'),
                'warning_validations': sum(1 for r in validation_results.values() if r.get('status') == 'warning'),
                'failed_validations': sum(1 for r in validation_results.values() if r.get('status') == 'error'),
                'lstm_ready': overall_score >= thresholds['acceptable'] and len(issues) == 0
            },
            'configuration': self.config
        }

        # Store results
        self.validation_results = comprehensive_report
        self.quality_score = overall_score
        self.recommendations = recommendations
        self.critical_issues = issues

        logger.info(f"Comprehensive validation completed: {quality_level} quality ({overall_score:.3f})")
        return comprehensive_report

    def _generate_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """
        Generate specific recommendations based on validation results.

        Args:
            validation_results: Individual validation results

        Returns:
            List of specific recommendations
        """
        recommendations = []

        # Trading hours recommendations
        trading_hours = validation_results.get('trading_hours', {})
        if trading_hours.get('coverage_ratio', 1.0) < 0.8:
            recommendations.append("Filter data to include only NY trading hours (09:30-17:00)")

        # Volume recommendations
        volume_data = validation_results.get('volume_data', {})
        if volume_data.get('zero_volume_ratio', 0) > 0.05:
            recommendations.append("Remove or interpolate bars with zero volume")
        if volume_data.get('negative_volume_count', 0) > 0:
            recommendations.append("Fix negative volume values (data error)")

        # Price recommendations
        price_data = validation_results.get('price_data', {})
        if price_data.get('ohlc_consistency_issues', 0) > 0:
            recommendations.append("Fix OHLC consistency issues (High < Low, etc.)")
        if price_data.get('missing_values', 0) > 0:
            recommendations.append("Handle missing price values through interpolation")

        # Completeness recommendations
        completeness = validation_results.get('data_completeness', {})
        if completeness.get('total_records', 0) < 10000:
            recommendations.append("Collect more historical data for robust training")
        if completeness.get('missing_ratio', 0) > 0.01:
            recommendations.append("Reduce missing data through better data collection")

        # Sequence recommendations
        sequence_quality = validation_results.get('sequence_quality', {})
        if sequence_quality.get('min_class_ratio', 1.0) < 0.05:
            recommendations.append("Address class imbalance through resampling or threshold adjustment")
        if sequence_quality.get('max_feature_correlation', 0) > 0.95:
            recommendations.append("Remove highly correlated features to reduce redundancy")

        return recommendations

    def generate_quality_report(self, output_path: str = "data_quality_report.json") -> None:
        """
        Generate and save comprehensive quality report.

        Args:
            output_path: Path to save the report
        """
        if not self.validation_results:
            logger.warning("No validation results available. Run validation first.")
            return

        # Add summary statistics
        report = self.validation_results.copy()
        report['report_metadata'] = {
            'generated_at': datetime.now().isoformat(),
            'validator_version': '1.0',
            'total_recommendations': len(self.recommendations),
            'total_critical_issues': len(self.critical_issues)
        }

        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        logger.info(f"Quality report saved to {output_path}")

    def is_lstm_ready(self) -> bool:
        """
        Check if data is ready for LSTM training.

        Returns:
            True if data meets LSTM training requirements
        """
        if not self.validation_results:
            return False

        return (self.quality_score >= self.config['quality_thresholds']['acceptable'] and
                len(self.critical_issues) == 0)

    def get_quality_summary(self) -> Dict[str, Any]:
        """
        Get concise quality summary.

        Returns:
            Dictionary with quality summary
        """
        if not self.validation_results:
            return {'status': 'not_validated'}

        return {
            'overall_score': self.quality_score,
            'quality_level': self.validation_results.get('quality_level', 'unknown'),
            'lstm_ready': self.is_lstm_ready(),
            'critical_issues_count': len(self.critical_issues),
            'recommendations_count': len(self.recommendations),
            'validation_summary': self.validation_results.get('summary', {})
        }
