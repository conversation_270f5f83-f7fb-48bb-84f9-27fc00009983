"""
Enhanced Target Label Generator for Balanced LSTM Gold Trading Strategy
======================================================================

This enhanced version includes aggressive filtering to achieve balanced class distribution:
- Higher confidence thresholds for buy signals
- Lower confidence thresholds for sell signals
- Volatility and trend strength filtering
- Reduced hold threshold to allow more trading signals

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class EnhancedTargetLabelGenerator:
    """
    Enhanced target label generator with aggressive balance filtering.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize enhanced target label generator.
        
        Args:
            config: Configuration dictionary with enhanced parameters
        """
        # Default enhanced configuration
        self.config = config or {
            'take_profit_pips': 30,
            'stop_loss_pips': 15,
            'time_horizon_bars': 6,
            'risk_reward_ratio': 2.0,
            'pip_value': 0.01,
            'min_price_movement': 0.03,
            'hold_threshold': 0.2,
            'buy_confidence_threshold': 0.8,
            'sell_confidence_threshold': 0.2,
            'volatility_threshold': 0.5,
            'trend_strength_threshold': 0.3,
            'label_encoding': {
                'hold': 0,
                'buy': 1,
                'sell': 2
            }
        }
        
        # Label generation statistics
        self.generation_stats = {
            'total_analyzed': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'hold_signals': 0,
            'filtered_by_volatility': 0,
            'filtered_by_confidence': 0
        }
        
        logger.info("Enhanced target label generator initialized")
    
    def calculate_pip_movement(self, price1: float, price2: float) -> float:
        """
        Calculate pip movement between two prices.
        
        Args:
            price1: Starting price
            price2: Ending price
            
        Returns:
            Pip movement
        """
        return (price2 - price1) / self.config['pip_value']
    
    def analyze_market_context(self, df: pd.DataFrame, start_idx: int, lookback: int = 10) -> Dict[str, float]:
        """
        Analyze market context around the current position.
        
        Args:
            df: OHLC DataFrame
            start_idx: Current position index
            lookback: Number of bars to look back
            
        Returns:
            Dictionary with market context metrics
        """
        start_lookback = max(0, start_idx - lookback)
        context_data = df.iloc[start_lookback:start_idx+1]
        
        if len(context_data) < 2:
            return {'volatility': 0, 'trend_strength': 0, 'price_momentum': 0}
        
        # Calculate volatility
        returns = context_data['close'].pct_change().dropna()
        volatility = returns.std() if len(returns) > 0 else 0
        
        # Calculate trend strength
        price_change = (context_data['close'].iloc[-1] - context_data['close'].iloc[0]) / context_data['close'].iloc[0]
        trend_strength = abs(price_change)
        
        # Calculate price momentum
        if len(context_data) >= 3:
            recent_change = (context_data['close'].iloc[-1] - context_data['close'].iloc[-3]) / context_data['close'].iloc[-3]
            price_momentum = recent_change
        else:
            price_momentum = 0
        
        return {
            'volatility': volatility,
            'trend_strength': trend_strength,
            'price_momentum': price_momentum
        }
    
    def analyze_trade_scenario_enhanced(self, df: pd.DataFrame, start_idx: int) -> str:
        """
        Analyze trade scenario with enhanced filtering for balanced labels.
        
        Args:
            df: OHLC DataFrame
            start_idx: Starting index for analysis
            
        Returns:
            Trade scenario ('buy', 'sell', 'hold')
        """
        self.generation_stats['total_analyzed'] += 1
        
        if start_idx >= len(df) - self.config['time_horizon_bars']:
            return 'hold'
        
        # Analyze market context
        market_context = self.analyze_market_context(df, start_idx)
        
        # Apply volatility filter
        if market_context['volatility'] < self.config['volatility_threshold'] / 1000:
            self.generation_stats['filtered_by_volatility'] += 1
            return 'hold'
        
        entry_price = df.iloc[start_idx]['close']
        
        # Calculate levels
        take_profit_buy = entry_price + (self.config['take_profit_pips'] * self.config['pip_value'])
        stop_loss_buy = entry_price - (self.config['stop_loss_pips'] * self.config['pip_value'])
        take_profit_sell = entry_price - (self.config['take_profit_pips'] * self.config['pip_value'])
        stop_loss_sell = entry_price + (self.config['stop_loss_pips'] * self.config['pip_value'])
        
        # Analyze forward price action
        end_idx = min(start_idx + self.config['time_horizon_bars'], len(df))
        future_data = df.iloc[start_idx+1:end_idx]
        
        if len(future_data) == 0:
            return 'hold'
        
        # Analyze buy scenario
        buy_analysis = self._analyze_scenario(future_data, entry_price, take_profit_buy, stop_loss_buy, 'buy')
        
        # Analyze sell scenario
        sell_analysis = self._analyze_scenario(future_data, entry_price, take_profit_sell, stop_loss_sell, 'sell')
        
        # Apply enhanced decision logic
        decision = self._make_enhanced_decision(buy_analysis, sell_analysis, market_context)
        
        # Update statistics
        if decision == 'buy':
            self.generation_stats['buy_signals'] += 1
        elif decision == 'sell':
            self.generation_stats['sell_signals'] += 1
        else:
            self.generation_stats['hold_signals'] += 1
        
        return decision
    
    def _analyze_scenario(self, future_data: pd.DataFrame, entry_price: float, 
                         take_profit: float, stop_loss: float, direction: str) -> Dict[str, Any]:
        """
        Analyze a specific trade scenario.
        
        Args:
            future_data: Forward price data
            entry_price: Entry price
            take_profit: Take profit level
            stop_loss: Stop loss level
            direction: Trade direction ('buy' or 'sell')
            
        Returns:
            Dictionary with scenario analysis
        """
        analysis = {
            'direction': direction,
            'success': False,
            'bars_to_tp': None,
            'bars_to_sl': None,
            'speed_score': 0,
            'confidence_score': 0
        }
        
        tp_hit = False
        sl_hit = False
        tp_bar = None
        sl_bar = None
        
        for i, (_, row) in enumerate(future_data.iterrows()):
            if direction == 'buy':
                if not tp_hit and row['high'] >= take_profit:
                    tp_hit = True
                    tp_bar = i + 1
                if not sl_hit and row['low'] <= stop_loss:
                    sl_hit = True
                    sl_bar = i + 1
            else:  # sell
                if not tp_hit and row['low'] <= take_profit:
                    tp_hit = True
                    tp_bar = i + 1
                if not sl_hit and row['high'] >= stop_loss:
                    sl_hit = True
                    sl_bar = i + 1
            
            # Break if both hit
            if tp_hit and sl_hit:
                break
        
        # Determine success
        if tp_hit and sl_hit:
            analysis['success'] = tp_bar <= sl_bar
            analysis['bars_to_tp'] = tp_bar
            analysis['bars_to_sl'] = sl_bar
        elif tp_hit:
            analysis['success'] = True
            analysis['bars_to_tp'] = tp_bar
        elif sl_hit:
            analysis['success'] = False
            analysis['bars_to_sl'] = sl_bar
        
        # Calculate speed score (faster is better)
        if analysis['success'] and analysis['bars_to_tp']:
            analysis['speed_score'] = 1.0 / analysis['bars_to_tp']
        
        # Calculate confidence score
        if analysis['success']:
            # Higher confidence for faster, cleaner wins
            time_factor = 1.0 / (analysis['bars_to_tp'] + 1) if analysis['bars_to_tp'] else 0
            analysis['confidence_score'] = min(1.0, time_factor * 2)
        
        return analysis
    
    def _make_enhanced_decision(self, buy_analysis: Dict[str, Any], sell_analysis: Dict[str, Any], 
                               market_context: Dict[str, float]) -> str:
        """
        Make enhanced trading decision with aggressive filtering.
        
        Args:
            buy_analysis: Buy scenario analysis
            sell_analysis: Sell scenario analysis
            market_context: Market context metrics
            
        Returns:
            Trading decision ('buy', 'sell', 'hold')
        """
        buy_confidence_threshold = self.config['buy_confidence_threshold']
        sell_confidence_threshold = self.config['sell_confidence_threshold']
        
        # Apply trend bias to confidence thresholds
        trend_bias = market_context['price_momentum']
        
        # Adjust thresholds based on trend
        if trend_bias > 0.01:  # Strong uptrend
            buy_confidence_threshold *= 0.9  # Slightly easier buy signals
            sell_confidence_threshold *= 1.2  # Harder sell signals
        elif trend_bias < -0.01:  # Strong downtrend
            buy_confidence_threshold *= 1.2  # Harder buy signals
            sell_confidence_threshold *= 0.8  # Easier sell signals
        
        # Check buy scenario
        buy_valid = (buy_analysis['success'] and 
                    buy_analysis['confidence_score'] >= buy_confidence_threshold)
        
        # Check sell scenario
        sell_valid = (sell_analysis['success'] and 
                     sell_analysis['confidence_score'] >= sell_confidence_threshold)
        
        if buy_valid and sell_valid:
            # Both valid - choose based on confidence and speed
            buy_total_score = buy_analysis['confidence_score'] * buy_analysis['speed_score']
            sell_total_score = sell_analysis['confidence_score'] * sell_analysis['speed_score']
            
            if buy_total_score > sell_total_score:
                return 'buy'
            else:
                return 'sell'
        elif buy_valid:
            return 'buy'
        elif sell_valid:
            return 'sell'
        else:
            # Apply hold threshold
            if (buy_analysis['success'] or sell_analysis['success']) and np.random.random() > self.config['hold_threshold']:
                # Randomly assign some marginal cases to trading signals
                if buy_analysis['confidence_score'] > sell_analysis['confidence_score']:
                    return 'buy'
                else:
                    return 'sell'
            return 'hold'
    
    def generate_labels_for_sequences(self, df: pd.DataFrame, timestamps: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        Generate enhanced balanced labels for LSTM sequences.
        
        Args:
            df: OHLC DataFrame
            timestamps: Array of sequence timestamps
            
        Returns:
            Tuple of (labels_array, generation_report)
        """
        logger.info(f"Generating enhanced balanced labels for {len(timestamps)} sequences")
        
        # Reset statistics
        self.generation_stats = {
            'total_analyzed': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'hold_signals': 0,
            'filtered_by_volatility': 0,
            'filtered_by_confidence': 0
        }
        
        labels = []
        
        for i, timestamp in enumerate(timestamps):
            if i % 100 == 0:
                logger.info(f"Generated labels for {i}/{len(timestamps)} sequences")
            
            # Find corresponding index in DataFrame
            try:
                df_idx = df.index.get_loc(timestamp)
            except KeyError:
                # Find nearest timestamp
                nearest_idx = df.index.get_indexer([timestamp], method='nearest')[0]
                df_idx = nearest_idx
            
            # Generate label
            label = self.analyze_trade_scenario_enhanced(df, df_idx)
            labels.append(label)
        
        labels_array = np.array(labels)
        
        # Calculate distribution
        unique_labels, counts = np.unique(labels_array, return_counts=True)
        distribution = {label: int(count) for label, count in zip(unique_labels, counts)}
        
        # Create generation report
        generation_report = {
            'total_sequences': len(labels_array),
            'label_distribution': distribution,
            'generation_stats': self.generation_stats,
            'configuration': self.config,
            'generation_timestamp': datetime.now().isoformat()
        }
        
        logger.info("Enhanced label generation completed")
        logger.info(f"Distribution - Buy: {distribution.get('buy', 0)}, Sell: {distribution.get('sell', 0)}, Hold: {distribution.get('hold', 0)}")
        
        return labels_array, generation_report
    
    def encode_labels_onehot(self, labels_array: np.ndarray) -> np.ndarray:
        """
        Convert string labels to one-hot encoding.
        
        Args:
            labels_array: Array of string labels
            
        Returns:
            One-hot encoded labels
        """
        logger.info("Converting labels to one-hot encoding")
        
        # Create mapping
        label_to_idx = self.config['label_encoding']
        
        # Convert to indices
        indices = np.array([label_to_idx[label] for label in labels_array])
        
        # Create one-hot encoding
        num_classes = len(label_to_idx)
        onehot = np.zeros((len(indices), num_classes))
        onehot[np.arange(len(indices)), indices] = 1
        
        return onehot.astype(np.float32)
