"""
LSTM Data Formatting and Export Module for Gold Trading Strategy
================================================================

This module handles final data formatting for LSTM training including:
- Proper X_train/y_train formatting
- Train/validation/test splits with temporal ordering
- Data export to compressed formats (.npz, HDF5)
- Training-ready dataset preparation
- Performance optimization for large datasets

Key Features:
- Memory-efficient data handling
- Temporal split preservation
- Multiple export formats
- Training metadata generation
- Data integrity validation

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from datetime import datetime
import h5py
import json
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class LSTMDataFormatter:
    """
    Format and export data for LSTM training with proper splits and validation.
    Handles memory-efficient processing and multiple export formats.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize LSTM data formatter.
        
        Args:
            config: Configuration dictionary with formatting parameters
        """
        # Default configuration
        self.config = config or {
            'splits': {
                'train_ratio': 0.7,
                'validation_ratio': 0.15,
                'test_ratio': 0.15,
                'temporal_ordering': True
            },
            'export_formats': {
                'npz': True,
                'hdf5': True,
                'compression': True
            },
            'validation': {
                'check_shapes': True,
                'check_data_types': True,
                'check_temporal_order': True,
                'check_label_distribution': True
            },
            'memory_optimization': {
                'use_float32': True,
                'batch_processing': True,
                'clear_intermediate': True
            }
        }
        
        # Formatting statistics
        self.formatting_stats = {
            'original_shape': None,
            'final_shapes': {},
            'split_info': {},
            'export_info': {},
            'validation_results': {}
        }
        
        logger.info("LSTM data formatter initialized")
    
    def validate_input_data(self, sequences: np.ndarray, labels: np.ndarray, 
                           timestamps: np.ndarray) -> Dict[str, Any]:
        """
        Validate input data for formatting.
        
        Args:
            sequences: LSTM sequences array
            labels: Labels array (string or one-hot)
            timestamps: Timestamps array
            
        Returns:
            Dictionary with validation results
        """
        logger.info("Validating input data for formatting")
        
        validation_results = {
            'status': 'success',
            'issues': [],
            'warnings': [],
            'data_info': {}
        }
        
        # Basic shape validation
        if len(sequences) != len(labels) or len(sequences) != len(timestamps):
            validation_results['issues'].append(
                f"Length mismatch: sequences={len(sequences)}, labels={len(labels)}, timestamps={len(timestamps)}"
            )
            validation_results['status'] = 'error'
        
        # Sequence shape validation
        if len(sequences.shape) != 3:
            validation_results['issues'].append(f"Invalid sequence shape: {sequences.shape}, expected 3D")
            validation_results['status'] = 'error'
        
        # Data type validation
        if not np.issubdtype(sequences.dtype, np.floating):
            validation_results['warnings'].append(f"Sequences not float type: {sequences.dtype}")
        
        # Missing value check
        if np.isnan(sequences).any():
            nan_count = np.isnan(sequences).sum()
            validation_results['warnings'].append(f"Found {nan_count} NaN values in sequences")
        
        # Temporal ordering check
        if len(timestamps) > 1:
            timestamps_sorted = np.all(timestamps[:-1] <= timestamps[1:])
            if not timestamps_sorted:
                validation_results['warnings'].append("Timestamps not in chronological order")
        
        # Store data info
        validation_results['data_info'] = {
            'sequences_shape': sequences.shape,
            'labels_shape': labels.shape,
            'timestamps_shape': timestamps.shape,
            'sequences_dtype': str(sequences.dtype),
            'labels_dtype': str(labels.dtype),
            'memory_usage_mb': (sequences.nbytes + labels.nbytes + timestamps.nbytes) / (1024 * 1024)
        }
        
        logger.info(f"Input validation completed: {validation_results['status']}")
        return validation_results
    
    def create_temporal_splits(self, sequences: np.ndarray, labels: np.ndarray, 
                              timestamps: np.ndarray) -> Dict[str, Dict[str, np.ndarray]]:
        """
        Create train/validation/test splits maintaining temporal order.
        
        Args:
            sequences: LSTM sequences array
            labels: Labels array
            timestamps: Timestamps array
            
        Returns:
            Dictionary with split data
        """
        logger.info("Creating temporal splits")
        
        total_samples = len(sequences)
        
        # Calculate split indices
        train_ratio = self.config['splits']['train_ratio']
        val_ratio = self.config['splits']['validation_ratio']
        test_ratio = self.config['splits']['test_ratio']
        
        # Validate ratios
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("Split ratios must sum to 1.0")
        
        train_end = int(total_samples * train_ratio)
        val_end = int(total_samples * (train_ratio + val_ratio))
        
        # Create splits
        splits = {
            'train': {
                'X': sequences[:train_end],
                'y': labels[:train_end],
                'timestamps': timestamps[:train_end]
            },
            'validation': {
                'X': sequences[train_end:val_end],
                'y': labels[train_end:val_end],
                'timestamps': timestamps[train_end:val_end]
            },
            'test': {
                'X': sequences[val_end:],
                'y': labels[val_end:],
                'timestamps': timestamps[val_end:]
            }
        }
        
        # Log split information
        for split_name, split_data in splits.items():
            logger.info(f"{split_name.capitalize()} set: {len(split_data['X'])} samples "
                       f"({len(split_data['X'])/total_samples:.1%})")
        
        # Store split info
        self.formatting_stats['split_info'] = {
            split_name: {
                'sample_count': len(split_data['X']),
                'percentage': len(split_data['X']) / total_samples,
                'date_range': {
                    'start': split_data['timestamps'].min(),
                    'end': split_data['timestamps'].max()
                }
            }
            for split_name, split_data in splits.items()
        }
        
        return splits
    
    def optimize_data_types(self, splits: Dict[str, Dict[str, np.ndarray]]) -> Dict[str, Dict[str, np.ndarray]]:
        """
        Optimize data types for memory efficiency.
        
        Args:
            splits: Dictionary with split data
            
        Returns:
            Dictionary with optimized split data
        """
        if not self.config['memory_optimization']['use_float32']:
            return splits
        
        logger.info("Optimizing data types for memory efficiency")
        
        optimized_splits = {}
        
        for split_name, split_data in splits.items():
            optimized_splits[split_name] = {}
            
            # Convert sequences to float32
            if split_data['X'].dtype != np.float32:
                optimized_splits[split_name]['X'] = split_data['X'].astype(np.float32)
                logger.info(f"{split_name} X: {split_data['X'].dtype} -> float32")
            else:
                optimized_splits[split_name]['X'] = split_data['X']
            
            # Keep labels as is (usually int or float)
            optimized_splits[split_name]['y'] = split_data['y']
            
            # Keep timestamps as is
            optimized_splits[split_name]['timestamps'] = split_data['timestamps']
        
        return optimized_splits
    
    def validate_splits(self, splits: Dict[str, Dict[str, np.ndarray]]) -> Dict[str, Any]:
        """
        Validate split data quality and consistency.
        
        Args:
            splits: Dictionary with split data
            
        Returns:
            Dictionary with validation results
        """
        logger.info("Validating split data")
        
        validation_results = {
            'status': 'success',
            'issues': [],
            'warnings': [],
            'split_analysis': {}
        }
        
        for split_name, split_data in splits.items():
            X, y, timestamps = split_data['X'], split_data['y'], split_data['timestamps']
            
            split_analysis = {
                'sample_count': len(X),
                'X_shape': X.shape,
                'y_shape': y.shape,
                'X_dtype': str(X.dtype),
                'y_dtype': str(y.dtype),
                'memory_mb': (X.nbytes + y.nbytes) / (1024 * 1024),
                'temporal_span_days': (timestamps.max() - timestamps.min()).days if len(timestamps) > 0 else 0
            }
            
            # Check for data quality issues
            if np.isnan(X).any():
                validation_results['issues'].append(f"{split_name}: Contains NaN values in X")
            
            if np.isnan(y).any():
                validation_results['issues'].append(f"{split_name}: Contains NaN values in y")
            
            # Check label distribution for classification
            if len(y.shape) == 2 and y.shape[1] > 1:  # One-hot encoded
                class_counts = y.sum(axis=0)
                min_class_count = class_counts.min()
                if min_class_count == 0:
                    validation_results['warnings'].append(f"{split_name}: Empty class detected")
                elif min_class_count < 10:
                    validation_results['warnings'].append(f"{split_name}: Very small class ({min_class_count} samples)")
                
                split_analysis['class_distribution'] = class_counts.tolist()
            
            validation_results['split_analysis'][split_name] = split_analysis
        
        # Check temporal ordering across splits
        all_timestamps = np.concatenate([
            splits['train']['timestamps'],
            splits['validation']['timestamps'],
            splits['test']['timestamps']
        ])
        
        if not np.all(all_timestamps[:-1] <= all_timestamps[1:]):
            validation_results['warnings'].append("Temporal ordering not preserved across splits")
        
        if validation_results['issues']:
            validation_results['status'] = 'error'
        elif validation_results['warnings']:
            validation_results['status'] = 'warning'
        
        logger.info(f"Split validation completed: {validation_results['status']}")
        return validation_results
    
    def export_to_npz(self, splits: Dict[str, Dict[str, np.ndarray]], 
                     output_dir: str = ".") -> Dict[str, str]:
        """
        Export splits to compressed NPZ format.
        
        Args:
            splits: Dictionary with split data
            output_dir: Output directory
            
        Returns:
            Dictionary with exported file paths
        """
        logger.info("Exporting data to NPZ format")
        
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        exported_files = {}
        
        for split_name, split_data in splits.items():
            filename = f"lstm_{split_name}_final.npz"
            filepath = output_dir / filename
            
            # Prepare data for export
            export_data = {
                'X': split_data['X'],
                'y': split_data['y'],
                'timestamps': split_data['timestamps'],
                'metadata': {
                    'split_name': split_name,
                    'sample_count': len(split_data['X']),
                    'feature_count': split_data['X'].shape[2],
                    'sequence_length': split_data['X'].shape[1],
                    'export_timestamp': datetime.now().isoformat(),
                    'data_types': {
                        'X': str(split_data['X'].dtype),
                        'y': str(split_data['y'].dtype)
                    }
                }
            }
            
            # Export with compression
            if self.config['export_formats']['compression']:
                np.savez_compressed(filepath, **export_data)
            else:
                np.savez(filepath, **export_data)
            
            exported_files[split_name] = str(filepath)
            
            # Log file info
            file_size_mb = filepath.stat().st_size / (1024 * 1024)
            logger.info(f"Exported {split_name}: {filename} ({file_size_mb:.1f} MB)")
        
        return exported_files

    def export_to_hdf5(self, splits: Dict[str, Dict[str, np.ndarray]],
                      output_dir: str = ".") -> str:
        """
        Export all splits to a single HDF5 file.

        Args:
            splits: Dictionary with split data
            output_dir: Output directory

        Returns:
            Path to exported HDF5 file
        """
        logger.info("Exporting data to HDF5 format")

        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)

        filepath = output_dir / "lstm_dataset_final.h5"

        with h5py.File(filepath, 'w') as f:
            # Create groups for each split
            for split_name, split_data in splits.items():
                group = f.create_group(split_name)

                # Store arrays with compression
                compression = 'gzip' if self.config['export_formats']['compression'] else None

                group.create_dataset('X', data=split_data['X'], compression=compression)
                group.create_dataset('y', data=split_data['y'], compression=compression)

                # Convert timestamps to string format for HDF5 compatibility
                timestamps_str = np.array([str(ts) for ts in split_data['timestamps']], dtype='S19')
                group.create_dataset('timestamps', data=timestamps_str, compression=compression)

                # Store metadata as attributes
                group.attrs['sample_count'] = len(split_data['X'])
                group.attrs['feature_count'] = split_data['X'].shape[2]
                group.attrs['sequence_length'] = split_data['X'].shape[1]
                group.attrs['X_dtype'] = str(split_data['X'].dtype)
                group.attrs['y_dtype'] = str(split_data['y'].dtype)

            # Store global metadata
            f.attrs['export_timestamp'] = datetime.now().isoformat()
            f.attrs['total_splits'] = len(splits)
            f.attrs['format_version'] = '1.0'

        # Log file info
        file_size_mb = filepath.stat().st_size / (1024 * 1024)
        logger.info(f"Exported HDF5: lstm_dataset_final.h5 ({file_size_mb:.1f} MB)")

        return str(filepath)

    def generate_training_metadata(self, splits: Dict[str, Dict[str, np.ndarray]],
                                  validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate comprehensive metadata for LSTM training.

        Args:
            splits: Dictionary with split data
            validation_results: Validation results

        Returns:
            Dictionary with training metadata
        """
        logger.info("Generating training metadata")

        # Calculate overall statistics
        total_samples = sum(len(split_data['X']) for split_data in splits.values())
        total_memory_mb = sum(
            (split_data['X'].nbytes + split_data['y'].nbytes) / (1024 * 1024)
            for split_data in splits.values()
        )

        # Get data shapes
        sample_X = next(iter(splits.values()))['X']
        sample_y = next(iter(splits.values()))['y']

        metadata = {
            'dataset_info': {
                'total_samples': total_samples,
                'sequence_length': sample_X.shape[1],
                'feature_count': sample_X.shape[2],
                'num_classes': sample_y.shape[1] if len(sample_y.shape) > 1 else 1,
                'total_memory_mb': total_memory_mb
            },
            'splits': {
                split_name: {
                    'sample_count': len(split_data['X']),
                    'percentage': len(split_data['X']) / total_samples,
                    'X_shape': split_data['X'].shape,
                    'y_shape': split_data['y'].shape,
                    'memory_mb': (split_data['X'].nbytes + split_data['y'].nbytes) / (1024 * 1024),
                    'date_range': {
                        'start': str(split_data['timestamps'].min()),
                        'end': str(split_data['timestamps'].max()),
                        'span_days': (split_data['timestamps'].max() - split_data['timestamps'].min()).days
                    }
                }
                for split_name, split_data in splits.items()
            },
            'data_quality': validation_results,
            'recommended_training_params': {
                'batch_size': min(64, len(splits['train']['X']) // 10),
                'epochs': 100,
                'early_stopping_patience': 10,
                'learning_rate': 0.001,
                'validation_split': 0.0,  # We have separate validation set
                'shuffle': False  # Maintain temporal order
            },
            'export_info': {
                'export_timestamp': datetime.now().isoformat(),
                'formatter_version': '1.0',
                'configuration': self.config
            }
        }

        return metadata

    def format_for_lstm_training(self, sequences: np.ndarray, labels: np.ndarray,
                                timestamps: np.ndarray, output_dir: str = ".") -> Dict[str, Any]:
        """
        Complete LSTM data formatting pipeline.

        Args:
            sequences: LSTM sequences array
            labels: Labels array
            timestamps: Timestamps array
            output_dir: Output directory for exports

        Returns:
            Dictionary with formatting results and file paths
        """
        logger.info("Starting complete LSTM data formatting pipeline")

        # Store original shape
        self.formatting_stats['original_shape'] = sequences.shape

        # Step 1: Validate input data
        logger.info("Step 1: Validating input data")
        input_validation = self.validate_input_data(sequences, labels, timestamps)

        if input_validation['status'] == 'error':
            raise ValueError(f"Input validation failed: {input_validation['issues']}")

        # Step 2: Create temporal splits
        logger.info("Step 2: Creating temporal splits")
        splits = self.create_temporal_splits(sequences, labels, timestamps)

        # Step 3: Optimize data types
        logger.info("Step 3: Optimizing data types")
        splits = self.optimize_data_types(splits)

        # Step 4: Validate splits
        logger.info("Step 4: Validating splits")
        split_validation = self.validate_splits(splits)

        if split_validation['status'] == 'error':
            raise ValueError(f"Split validation failed: {split_validation['issues']}")

        # Step 5: Export data
        logger.info("Step 5: Exporting formatted data")
        exported_files = {}

        if self.config['export_formats']['npz']:
            npz_files = self.export_to_npz(splits, output_dir)
            exported_files.update(npz_files)

        if self.config['export_formats']['hdf5']:
            hdf5_file = self.export_to_hdf5(splits, output_dir)
            exported_files['hdf5'] = hdf5_file

        # Step 6: Generate metadata
        logger.info("Step 6: Generating training metadata")
        metadata = self.generate_training_metadata(splits, split_validation)

        # Save metadata
        metadata_file = Path(output_dir) / "lstm_training_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)

        exported_files['metadata'] = str(metadata_file)

        # Compile results
        formatting_results = {
            'status': 'success',
            'input_validation': input_validation,
            'split_validation': split_validation,
            'exported_files': exported_files,
            'metadata': metadata,
            'formatting_stats': self.formatting_stats
        }

        # Update formatting stats
        self.formatting_stats.update({
            'final_shapes': {
                split_name: split_data['X'].shape
                for split_name, split_data in splits.items()
            },
            'export_info': exported_files,
            'validation_results': {
                'input': input_validation,
                'splits': split_validation
            }
        })

        logger.info("LSTM data formatting pipeline completed successfully")
        return formatting_results

    def load_formatted_data(self, filepath: str, split_name: str = None) -> Dict[str, np.ndarray]:
        """
        Load formatted data from exported files.

        Args:
            filepath: Path to data file (.npz or .h5)
            split_name: Specific split to load (for HDF5 files)

        Returns:
            Dictionary with loaded data
        """
        logger.info(f"Loading formatted data from {filepath}")

        filepath = Path(filepath)

        if filepath.suffix == '.npz':
            data = np.load(filepath, allow_pickle=True)
            return {
                'X': data['X'],
                'y': data['y'],
                'timestamps': data['timestamps'],
                'metadata': data['metadata'].item() if 'metadata' in data else {}
            }

        elif filepath.suffix == '.h5':
            with h5py.File(filepath, 'r') as f:
                if split_name:
                    if split_name not in f:
                        raise ValueError(f"Split '{split_name}' not found in HDF5 file")

                    group = f[split_name]
                    # Convert timestamps back from string format
                    timestamps_str = group['timestamps'][:]
                    timestamps = pd.to_datetime([ts.decode('utf-8') for ts in timestamps_str])
                    return {
                        'X': group['X'][:],
                        'y': group['y'][:],
                        'timestamps': timestamps,
                        'metadata': dict(group.attrs)
                    }
                else:
                    # Load all splits
                    all_data = {}
                    for split_name in f.keys():
                        group = f[split_name]
                        # Convert timestamps back from string format
                        timestamps_str = group['timestamps'][:]
                        timestamps = pd.to_datetime([ts.decode('utf-8') for ts in timestamps_str])
                        all_data[split_name] = {
                            'X': group['X'][:],
                            'y': group['y'][:],
                            'timestamps': timestamps,
                            'metadata': dict(group.attrs)
                        }
                    return all_data

        else:
            raise ValueError(f"Unsupported file format: {filepath.suffix}")

    def get_formatting_summary(self) -> Dict[str, Any]:
        """
        Get summary of formatting operations.

        Returns:
            Dictionary with formatting summary
        """
        return {
            'original_shape': self.formatting_stats.get('original_shape'),
            'final_shapes': self.formatting_stats.get('final_shapes', {}),
            'split_info': self.formatting_stats.get('split_info', {}),
            'export_info': self.formatting_stats.get('export_info', {}),
            'validation_status': {
                'input': self.formatting_stats.get('validation_results', {}).get('input', {}).get('status'),
                'splits': self.formatting_stats.get('validation_results', {}).get('splits', {}).get('status')
            }
        }
