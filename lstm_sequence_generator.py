"""
LSTM Sequence Generation Module for Gold Trading Strategy
========================================================

This module implements sliding window approach to create sequences of 48 timesteps
with 5 features each for LSTM training. It handles proper temporal ordering and
market gaps (weekends, holidays).

Key Features:
- 48-timestep lookback window (4 hours of 5-minute data)
- 5 features per timestep (normalized technical indicators)
- Proper handling of market gaps
- Temporal ordering preservation
- Memory-efficient sequence generation

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Dict, Any, Optional
import logging
from datetime import datetime, timedelta
import gc
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class LSTMSequenceGenerator:
    """
    Generate LSTM sequences from processed time series data.
    Implements sliding window approach with proper gap handling.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize LSTM sequence generator.
        
        Args:
            config: Configuration dictionary with sequence parameters
        """
        # Default configuration
        self.config = config or {
            'sequence_length': 48,  # 48 bars = 4 hours of 5-minute data
            'features_per_timestep': 5,  # 5 normalized indicators
            'min_gap_minutes': 10,  # Minimum gap to consider as market break
            'max_gap_hours': 72,  # Maximum gap to allow in sequence (3 days)
            'overlap_ratio': 0.0,  # No overlap between sequences (0.0 = no overlap, 0.5 = 50% overlap)
            'validation_checks': True,  # Enable data validation
            'memory_efficient': True  # Use memory-efficient generation
        }
        
        # Sequence statistics
        self.sequence_stats = {
            'total_sequences': 0,
            'valid_sequences': 0,
            'rejected_sequences': 0,
            'rejection_reasons': {},
            'feature_statistics': {}
        }
        
        logger.info("LSTM sequence generator initialized")
    
    def validate_input_data(self, df: pd.DataFrame, feature_columns: List[str]) -> bool:
        """
        Validate input data for sequence generation.
        
        Args:
            df: Input DataFrame
            feature_columns: List of feature columns
            
        Returns:
            True if data is valid, False otherwise
        """
        logger.info("Validating input data for sequence generation")
        
        # Check required columns
        missing_columns = [col for col in feature_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required feature columns: {missing_columns}")
            return False
        
        # Check data types
        for col in feature_columns:
            if not pd.api.types.is_numeric_dtype(df[col]):
                logger.error(f"Non-numeric data in column: {col}")
                return False
        
        # Check for sufficient data
        min_required_rows = self.config['sequence_length'] * 2
        if len(df) < min_required_rows:
            logger.error(f"Insufficient data: {len(df)} rows, need at least {min_required_rows}")
            return False
        
        # Check datetime index
        if not isinstance(df.index, pd.DatetimeIndex):
            logger.error("DataFrame must have DatetimeIndex")
            return False
        
        # Check for proper sorting
        if not df.index.is_monotonic_increasing:
            logger.warning("Data is not sorted by datetime, sorting now")
            df.sort_index(inplace=True)
        
        # Check missing values
        missing_values = df[feature_columns].isnull().sum().sum()
        if missing_values > 0:
            logger.warning(f"Found {missing_values} missing values in features")
        
        logger.info("Input data validation completed successfully")
        return True
    
    def detect_market_gaps(self, df: pd.DataFrame) -> List[Tuple[datetime, datetime]]:
        """
        Detect market gaps (weekends, holidays) in the data.
        
        Args:
            df: Input DataFrame with DatetimeIndex
            
        Returns:
            List of tuples (gap_start, gap_end)
        """
        logger.info("Detecting market gaps")
        
        gaps = []
        min_gap_minutes = self.config['min_gap_minutes']
        max_gap_hours = self.config['max_gap_hours']
        
        # Calculate time differences
        time_diffs = df.index.to_series().diff()
        
        # Find gaps larger than minimum threshold
        gap_mask = time_diffs > timedelta(minutes=min_gap_minutes)
        gap_indices = df.index[gap_mask]
        
        for gap_end in gap_indices:
            gap_start = df.index[df.index < gap_end][-1]  # Previous timestamp
            gap_duration = gap_end - gap_start
            
            # Only consider gaps within reasonable limits
            if gap_duration <= timedelta(hours=max_gap_hours):
                gaps.append((gap_start, gap_end))
        
        logger.info(f"Detected {len(gaps)} market gaps")
        return gaps
    
    def is_valid_sequence_window(self, start_idx: int, end_idx: int, 
                                df: pd.DataFrame, gaps: List[Tuple[datetime, datetime]]) -> Tuple[bool, str]:
        """
        Check if a sequence window is valid (no major gaps).
        
        Args:
            start_idx: Start index of sequence
            end_idx: End index of sequence
            df: DataFrame with data
            gaps: List of detected gaps
            
        Returns:
            Tuple of (is_valid, rejection_reason)
        """
        if start_idx < 0 or end_idx >= len(df):
            return False, "index_out_of_bounds"
        
        sequence_start = df.index[start_idx]
        sequence_end = df.index[end_idx]
        
        # Check if any major gaps fall within this sequence
        for gap_start, gap_end in gaps:
            if gap_start >= sequence_start and gap_end <= sequence_end:
                gap_duration = gap_end - gap_start
                if gap_duration > timedelta(hours=1):  # More than 1 hour gap
                    return False, "contains_major_gap"
        
        # Check for missing values in the sequence
        sequence_data = df.iloc[start_idx:end_idx + 1]
        if sequence_data.isnull().any().any():
            return False, "contains_missing_values"
        
        return True, "valid"
    
    def generate_sequences_batch(self, df: pd.DataFrame, feature_columns: List[str], 
                                batch_size: int = 1000) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        Generate LSTM sequences in batches for memory efficiency.
        
        Args:
            df: Input DataFrame
            feature_columns: List of feature columns
            batch_size: Number of sequences per batch
            
        Returns:
            Tuple of (sequences_array, timestamps_array, generation_stats)
        """
        logger.info(f"Generating LSTM sequences in batches of {batch_size}")
        
        sequence_length = self.config['sequence_length']
        overlap_ratio = self.config['overlap_ratio']
        
        # Calculate step size based on overlap
        step_size = max(1, int(sequence_length * (1 - overlap_ratio)))
        
        # Detect market gaps
        gaps = self.detect_market_gaps(df)
        
        # Calculate potential sequence positions
        max_start_idx = len(df) - sequence_length
        sequence_positions = list(range(0, max_start_idx, step_size))
        
        logger.info(f"Potential sequence positions: {len(sequence_positions)}")
        
        # Generate sequences in batches
        all_sequences = []
        all_timestamps = []
        valid_count = 0
        rejected_count = 0
        rejection_reasons = {}
        
        for i in range(0, len(sequence_positions), batch_size):
            batch_positions = sequence_positions[i:i + batch_size]
            batch_sequences = []
            batch_timestamps = []
            
            for start_idx in batch_positions:
                end_idx = start_idx + sequence_length - 1
                
                # Validate sequence window
                is_valid, reason = self.is_valid_sequence_window(start_idx, end_idx, df, gaps)
                
                if is_valid:
                    # Extract sequence data more memory efficiently
                    try:
                        # Use .values directly on the slice to avoid memory issues
                        sequence_slice = df.iloc[start_idx:start_idx + sequence_length]
                        sequence_data = sequence_slice[feature_columns].values.astype(np.float32)
                        sequence_timestamp = df.index[end_idx]  # Use end timestamp

                        batch_sequences.append(sequence_data)
                        batch_timestamps.append(sequence_timestamp)
                        valid_count += 1
                    except MemoryError:
                        logger.warning(f"Memory error at sequence {start_idx}, skipping")
                        rejected_count += 1
                        rejection_reasons['memory_error'] = rejection_reasons.get('memory_error', 0) + 1
                else:
                    rejected_count += 1
                    rejection_reasons[reason] = rejection_reasons.get(reason, 0) + 1
            
            if batch_sequences:
                all_sequences.extend(batch_sequences)
                all_timestamps.extend(batch_timestamps)

            # Clear batch data and run garbage collection
            del batch_sequences, batch_timestamps
            gc.collect()

            # Log progress
            if (i // batch_size + 1) % 5 == 0:  # Log more frequently
                logger.info(f"Processed {i + len(batch_positions)} positions, "
                           f"valid: {valid_count}, rejected: {rejected_count}")
        
        # Convert to numpy arrays
        if all_sequences:
            sequences_array = np.array(all_sequences, dtype=np.float32)
            timestamps_array = np.array(all_timestamps)
        else:
            sequences_array = np.empty((0, sequence_length, len(feature_columns)), dtype=np.float32)
            timestamps_array = np.array([])
        
        # Update statistics
        self.sequence_stats.update({
            'total_sequences': len(sequence_positions),
            'valid_sequences': valid_count,
            'rejected_sequences': rejected_count,
            'rejection_reasons': rejection_reasons
        })
        
        generation_stats = {
            'total_potential': len(sequence_positions),
            'valid_generated': valid_count,
            'rejected': rejected_count,
            'rejection_reasons': rejection_reasons,
            'final_shape': sequences_array.shape,
            'memory_usage_mb': sequences_array.nbytes / (1024 * 1024) if len(all_sequences) > 0 else 0
        }
        
        logger.info(f"Sequence generation completed: {valid_count} valid sequences generated")
        logger.info(f"Final shape: {sequences_array.shape}")
        logger.info(f"Memory usage: {generation_stats['memory_usage_mb']:.1f} MB")
        
        return sequences_array, timestamps_array, generation_stats
    
    def validate_generated_sequences(self, sequences: np.ndarray, 
                                   feature_columns: List[str]) -> Dict[str, Any]:
        """
        Validate generated sequences for quality and consistency.
        
        Args:
            sequences: Generated sequences array
            feature_columns: List of feature column names
            
        Returns:
            Dictionary with validation results
        """
        logger.info("Validating generated sequences")
        
        if len(sequences) == 0:
            return {'status': 'error', 'message': 'No sequences to validate'}
        
        validation_results = {
            'status': 'success',
            'sequence_count': len(sequences),
            'sequence_shape': sequences.shape,
            'feature_statistics': {},
            'quality_checks': {},
            'issues': []
        }
        
        # Check shape consistency
        expected_shape = (len(sequences), self.config['sequence_length'], len(feature_columns))
        if sequences.shape != expected_shape:
            validation_results['issues'].append(f"Shape mismatch: got {sequences.shape}, expected {expected_shape}")
        
        # Check for NaN values
        nan_count = np.isnan(sequences).sum()
        if nan_count > 0:
            validation_results['issues'].append(f"Found {nan_count} NaN values in sequences")
        
        # Check for infinite values
        inf_count = np.isinf(sequences).sum()
        if inf_count > 0:
            validation_results['issues'].append(f"Found {inf_count} infinite values in sequences")
        
        # Calculate feature statistics
        for i, feature_name in enumerate(feature_columns):
            feature_data = sequences[:, :, i]
            validation_results['feature_statistics'][feature_name] = {
                'mean': float(np.mean(feature_data)),
                'std': float(np.std(feature_data)),
                'min': float(np.min(feature_data)),
                'max': float(np.max(feature_data)),
                'nan_count': int(np.isnan(feature_data).sum())
            }
        
        # Quality checks
        validation_results['quality_checks'] = {
            'all_finite': np.all(np.isfinite(sequences)),
            'proper_range': np.all((sequences >= -1.1) & (sequences <= 1.1)),  # Allow small tolerance
            'no_constant_sequences': len(np.unique(sequences.reshape(-1, sequences.shape[-1]), axis=0)) > 1
        }
        
        # Overall status
        if validation_results['issues']:
            validation_results['status'] = 'issues_found'
        
        logger.info(f"Sequence validation completed: {validation_results['status']}")
        return validation_results

    def generate_sequences(self, df: pd.DataFrame, feature_columns: List[str]) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        Main method to generate LSTM sequences from processed data.

        Args:
            df: Input DataFrame with processed features
            feature_columns: List of feature columns to use

        Returns:
            Tuple of (sequences, timestamps, generation_report)
        """
        logger.info("Starting LSTM sequence generation")

        # Validate input data
        if not self.validate_input_data(df, feature_columns):
            raise ValueError("Input data validation failed")

        # Generate sequences with smaller batch size for memory efficiency
        sequences, timestamps, generation_stats = self.generate_sequences_batch(
            df, feature_columns, batch_size=50
        )

        # Validate generated sequences
        validation_results = self.validate_generated_sequences(sequences, feature_columns)

        # Compile generation report
        generation_report = {
            'input_data': {
                'total_records': len(df),
                'date_range': {
                    'start': df.index.min(),
                    'end': df.index.max()
                },
                'features_used': feature_columns
            },
            'generation_stats': generation_stats,
            'validation_results': validation_results,
            'configuration': self.config,
            'sequence_statistics': self.sequence_stats
        }

        logger.info(f"LSTM sequence generation completed successfully")
        logger.info(f"Generated {len(sequences)} sequences with shape {sequences.shape}")

        return sequences, timestamps, generation_report

    def save_sequences(self, sequences: np.ndarray, timestamps: np.ndarray,
                      filepath: str, compression: bool = True) -> None:
        """
        Save generated sequences to file.

        Args:
            sequences: Generated sequences array
            timestamps: Corresponding timestamps
            filepath: Output file path
            compression: Whether to use compression
        """
        logger.info(f"Saving sequences to {filepath}")

        save_dict = {
            'sequences': sequences,
            'timestamps': timestamps,
            'config': self.config,
            'stats': self.sequence_stats
        }

        if compression:
            np.savez_compressed(filepath, **save_dict)
        else:
            np.savez(filepath, **save_dict)

        file_size_mb = sequences.nbytes / (1024 * 1024)
        logger.info(f"Sequences saved: {len(sequences)} sequences, {file_size_mb:.1f} MB")

    def load_sequences(self, filepath: str) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        Load sequences from file.

        Args:
            filepath: Input file path

        Returns:
            Tuple of (sequences, timestamps, metadata)
        """
        logger.info(f"Loading sequences from {filepath}")

        data = np.load(filepath, allow_pickle=True)

        sequences = data['sequences']
        timestamps = data['timestamps']

        metadata = {
            'config': data['config'].item() if 'config' in data else {},
            'stats': data['stats'].item() if 'stats' in data else {}
        }

        logger.info(f"Loaded {len(sequences)} sequences with shape {sequences.shape}")
        return sequences, timestamps, metadata

    def get_sequence_summary(self, sequences: np.ndarray, timestamps: np.ndarray) -> Dict[str, Any]:
        """
        Get comprehensive summary of generated sequences.

        Args:
            sequences: Generated sequences array
            timestamps: Corresponding timestamps

        Returns:
            Dictionary with sequence summary
        """
        if len(sequences) == 0:
            return {'error': 'No sequences to summarize'}

        summary = {
            'basic_info': {
                'total_sequences': len(sequences),
                'sequence_length': sequences.shape[1],
                'features_per_timestep': sequences.shape[2],
                'total_data_points': sequences.size,
                'memory_usage_mb': sequences.nbytes / (1024 * 1024)
            },
            'temporal_info': {
                'date_range': {
                    'start': timestamps.min(),
                    'end': timestamps.max()
                },
                'span_days': (timestamps.max() - timestamps.min()).days,
                'average_interval': np.mean(np.diff(timestamps))
            },
            'data_quality': {
                'nan_count': int(np.isnan(sequences).sum()),
                'inf_count': int(np.isinf(sequences).sum()),
                'finite_ratio': float(np.isfinite(sequences).mean()),
                'value_range': {
                    'min': float(np.min(sequences)),
                    'max': float(np.max(sequences)),
                    'mean': float(np.mean(sequences)),
                    'std': float(np.std(sequences))
                }
            },
            'feature_analysis': {}
        }

        # Per-feature analysis
        for i in range(sequences.shape[2]):
            feature_data = sequences[:, :, i]
            summary['feature_analysis'][f'feature_{i}'] = {
                'mean': float(np.mean(feature_data)),
                'std': float(np.std(feature_data)),
                'min': float(np.min(feature_data)),
                'max': float(np.max(feature_data)),
                'skewness': float(self._calculate_skewness(feature_data.flatten())),
                'kurtosis': float(self._calculate_kurtosis(feature_data.flatten()))
            }

        return summary

    def _calculate_skewness(self, data: np.ndarray) -> float:
        """Calculate skewness of data."""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 3)

    def _calculate_kurtosis(self, data: np.ndarray) -> float:
        """Calculate kurtosis of data."""
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0.0
        return np.mean(((data - mean) / std) ** 4) - 3

    def split_sequences_temporal(self, sequences: np.ndarray, timestamps: np.ndarray,
                               train_ratio: float = 0.7, val_ratio: float = 0.15,
                               test_ratio: float = 0.15) -> Tuple[Dict[str, np.ndarray], Dict[str, np.ndarray]]:
        """
        Split sequences into train/validation/test sets maintaining temporal order.

        Args:
            sequences: Generated sequences array
            timestamps: Corresponding timestamps
            train_ratio: Ratio for training set
            val_ratio: Ratio for validation set
            test_ratio: Ratio for test set

        Returns:
            Tuple of (sequence_splits, timestamp_splits)
        """
        logger.info("Splitting sequences with temporal ordering")

        # Validate ratios
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("Split ratios must sum to 1.0")

        total_sequences = len(sequences)

        # Calculate split indices
        train_end = int(total_sequences * train_ratio)
        val_end = int(total_sequences * (train_ratio + val_ratio))

        # Split sequences
        sequence_splits = {
            'train': sequences[:train_end],
            'validation': sequences[train_end:val_end],
            'test': sequences[val_end:]
        }

        # Split timestamps
        timestamp_splits = {
            'train': timestamps[:train_end],
            'validation': timestamps[train_end:val_end],
            'test': timestamps[val_end:]
        }

        # Log split information
        for split_name, split_data in sequence_splits.items():
            logger.info(f"{split_name.capitalize()} set: {len(split_data)} sequences "
                       f"({len(split_data)/total_sequences:.1%})")

        return sequence_splits, timestamp_splits
