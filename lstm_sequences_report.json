{"generation_report": {"input_data": {"total_records": 484717, "date_range": {"start": "2004-06-11 13:30:00", "end": "2025-09-24 18:20:00"}, "features_used": ["macd_histogram_slope_norm", "stochastic_d_norm", "dpo_norm", "bias_ratio_norm", "log_returns_norm"]}, "generation_stats": {"total_potential": 10098, "valid_generated": 4837, "rejected": 5261, "rejection_reasons": {"contains_major_gap": 5261}, "final_shape": [4837, 48, 5], "memory_usage_mb": 4.42840576171875}, "validation_results": {"status": "success", "sequence_count": 4837, "sequence_shape": [4837, 48, 5], "feature_statistics": {"macd_histogram_slope_norm": {"mean": 0.0016878412570804358, "std": 0.46529388427734375, "min": -1.0, "max": 1.0, "nan_count": 0}, "stochastic_d_norm": {"mean": 0.03180599957704544, "std": 0.5172167420387268, "min": -1.0, "max": 1.0, "nan_count": 0}, "dpo_norm": {"mean": -0.0028294639196246862, "std": 0.47404611110687256, "min": -1.0, "max": 1.0, "nan_count": 0}, "bias_ratio_norm": {"mean": -0.004922543652355671, "std": 0.4565027356147766, "min": -1.0, "max": 1.0, "nan_count": 0}, "log_returns_norm": {"mean": -0.0005792449810542166, "std": 0.4674837589263916, "min": -1.0, "max": 1.0, "nan_count": 0}}, "quality_checks": {"all_finite": "True", "proper_range": "True", "no_constant_sequences": true}, "issues": []}, "configuration": {"sequence_length": 48, "features_per_timestep": 5, "min_gap_minutes": 10, "max_gap_hours": 72, "overlap_ratio": 0.0, "validation_checks": true, "memory_efficient": true}, "sequence_statistics": {"total_sequences": 10098, "valid_sequences": 4837, "rejected_sequences": 5261, "rejection_reasons": {"contains_major_gap": 5261}, "feature_statistics": {}}}, "sequence_summary": {"basic_info": {"total_sequences": 4837, "sequence_length": 48, "features_per_timestep": 5, "total_data_points": 1160880, "memory_usage_mb": 4.42840576171875}, "temporal_info": {"date_range": {"start": "2004-06-14 18:45:00", "end": "2025-09-23 20:50:00"}, "span_days": 7771, "average_interval": "1 days 14:33:58.275434243"}, "data_quality": {"nan_count": 0, "inf_count": 0, "finite_ratio": 1.0, "value_range": {"min": -1.0, "max": 1.0, "mean": 0.0050325170159339905, "std": 0.4767782390117645}}, "feature_analysis": {"feature_0": {"mean": 0.0016878412570804358, "std": 0.46529388427734375, "min": -1.0, "max": 1.0, "skewness": 0.0011354070156812668, "kurtosis": 0.025502681732177734}, "feature_1": {"mean": 0.03180599957704544, "std": 0.5172167420387268, "min": -1.0, "max": 1.0, "skewness": -0.04360382631421089, "kurtosis": -1.2469596862792969}, "feature_2": {"mean": -0.0028294639196246862, "std": 0.47404611110687256, "min": -1.0, "max": 1.0, "skewness": 0.0006595548475161195, "kurtosis": 0.04250907897949219}, "feature_3": {"mean": -0.004922543652355671, "std": 0.4565027356147766, "min": -1.0, "max": 1.0, "skewness": -0.01787317730486393, "kurtosis": 0.11150884628295898}, "feature_4": {"mean": -0.0005792449810542166, "std": 0.4674837589263916, "min": -1.0, "max": 1.0, "skewness": -0.0028885428328067064, "kurtosis": -0.04229307174682617}}}, "split_info": {"train": 3385, "validation": 726, "test": 726}}