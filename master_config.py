"""
Master Configuration System for LSTM Gold Trading Strategy
==========================================================

This module provides centralized configuration management for the entire
LSTM-based gold trading pipeline including:
- Data preparation and filtering
- Technical indicator calculations
- LSTM sequence generation
- Target label generation
- Model training parameters
- Live trading settings
- Risk management parameters

Author: AI Assistant
Date: 2025-01-24
"""

import os
from typing import Dict, Any, Optional
from datetime import datetime
import json
from pathlib import Path

class MasterConfig:
    """
    Master configuration manager for LSTM gold trading strategy.
    """
    
    def __init__(self, environment: str = "development"):
        """
        Initialize master configuration.
        
        Args:
            environment: Environment type ('development', 'production', 'backtesting')
        """
        self.environment = environment
        self.config = self._load_base_config()
        self._apply_environment_overrides()
    
    def _load_base_config(self) -> Dict[str, Any]:
        """
        Load base configuration with all default parameters.
        
        Returns:
            Base configuration dictionary
        """
        return {
            # ============================================================================
            # DATA PREPARATION CONFIGURATION
            # ============================================================================
            'data_preparation': {
                'symbol': 'XAUUSD!',  # MT5 symbol (not XAUUSD)
                'timeframe': '5m',
                'years_back': 3,  # Use only recent years for balanced labels
                'trading_hours': {
                    'start_hour': 9,   # 09:30 NY time
                    'start_minute': 30,
                    'end_hour': 17,    # 17:00 NY time
                    'end_minute': 0,
                    'timezone': 'America/New_York'
                },
                'data_quality': {
                    'min_volume_threshold': 1,
                    'max_spread_pips': 5,
                    'remove_weekends': True,
                    'remove_holidays': True,
                    'fill_gaps': True,
                    'max_gap_minutes': 15
                }
            },
            
            # ============================================================================
            # TECHNICAL INDICATORS CONFIGURATION
            # ============================================================================
            'technical_indicators': {
                'macd_histogram_slope': {
                    'fast_period': 12,
                    'slow_period': 26,
                    'signal_period': 9,
                    'slope_window': 5,  # Linear regression window
                    'normalization_range': (-1, 1)
                },
                'stochastic': {
                    'k_period': 14,
                    'd_period': 3,
                    'smooth_k': 3,
                    'normalization_range': (0, 1)
                },
                'dpo': {
                    'period': 10,
                    'normalization_range': (-1, 1)
                },
                'bias_ratio': {
                    'period': 20,
                    'normalization_range': (-1, 1)
                },
                'log_returns': {
                    'normalization_range': (-1, 1),
                    'outlier_threshold': 3  # Standard deviations
                }
            },
            
            # ============================================================================
            # LSTM SEQUENCE GENERATION CONFIGURATION
            # ============================================================================
            'lstm_sequences': {
                'sequence_length': 48,  # 4 hours of 5-minute bars
                'features_per_timestep': 5,
                'overlap_ratio': 0.0,  # No overlap for independent samples
                'min_gap_minutes': 10,
                'max_gap_hours': 72,
                'validation_checks': True,
                'memory_efficient': True,
                'batch_size': 50
            },
            
            # ============================================================================
            # TARGET LABEL GENERATION CONFIGURATION
            # ============================================================================
            'target_labels': {
                'take_profit_pips': 25,
                'stop_loss_pips': 12,
                'time_horizon_bars': 8,  # 40 minutes
                'risk_reward_ratio': 2.0,
                'pip_value': 0.01,
                'min_price_movement': 0.02,
                'hold_threshold': 0.4,
                'buy_confidence_threshold': 0.3,
                'sell_confidence_threshold': 0.6,
                'volatility_threshold': 0.3,
                'trend_strength_threshold': 0.2,
                'label_encoding': {
                    'hold': 0,
                    'buy': 1,
                    'sell': 2
                },
                'target_distribution': {
                    'buy': (15.0, 30.0),   # 15-30%
                    'sell': (15.0, 70.0),  # 15-70%
                    'hold': (15.0, 40.0)   # 15-40%
                }
            },
            
            # ============================================================================
            # LSTM MODEL CONFIGURATION
            # ============================================================================
            'lstm_model': {
                'architecture': {
                    'lstm_layers': [
                        {'units': 64, 'return_sequences': True, 'dropout': 0.2},
                        {'units': 32, 'return_sequences': False, 'dropout': 0.2}
                    ],
                    'dense_layers': [
                        {'units': 16, 'activation': 'relu'},
                        {'units': 3, 'activation': 'softmax'}  # 3 classes: hold, buy, sell
                    ]
                },
                'training': {
                    'batch_size': 32,
                    'epochs': 100,
                    'learning_rate': 0.001,
                    'optimizer': 'adam',
                    'loss': 'categorical_crossentropy',
                    'metrics': ['accuracy'],
                    'validation_split': 0.0,  # Use separate validation set
                    'shuffle': False,  # Maintain temporal order
                    'early_stopping': {
                        'monitor': 'val_loss',
                        'patience': 10,
                        'restore_best_weights': True
                    }
                },
                'performance_targets': {
                    'min_accuracy': 0.56,
                    'target_accuracy': 0.65,
                    'min_precision': 0.55,
                    'min_recall': 0.50,
                    'target_sharpe_ratio': 1.3
                }
            },
            
            # ============================================================================
            # DATA SPLITS CONFIGURATION
            # ============================================================================
            'data_splits': {
                'train_ratio': 0.7,
                'validation_ratio': 0.15,
                'test_ratio': 0.15,
                'temporal_ordering': True,
                'stratify': False  # Don't stratify due to temporal nature
            },
            
            # ============================================================================
            # RISK MANAGEMENT CONFIGURATION
            # ============================================================================
            'risk_management': {
                'position_sizing': {
                    'risk_per_trade': 0.0025,  # 0.25% of account per trade
                    'max_positions': 3,
                    'max_daily_risk': 0.02,  # 2% max daily risk
                    'max_drawdown': 0.15  # 15% max drawdown
                },
                'trade_management': {
                    'take_profit_pips': 25,
                    'stop_loss_pips': 12,
                    'trailing_stop': False,
                    'break_even_pips': 15,
                    'partial_close_pips': 20
                },
                'filters': {
                    'min_confidence': 0.6,  # Minimum model confidence
                    'max_spread_pips': 3,
                    'avoid_news_minutes': 30,
                    'trading_sessions': ['london', 'new_york']
                }
            },
            
            # ============================================================================
            # LIVE TRADING CONFIGURATION
            # ============================================================================
            'live_trading': {
                'execution': {
                    'update_interval_seconds': 30,  # 30-60 second intervals as specified
                    'max_execution_delay_ms': 500,
                    'slippage_tolerance_pips': 1,
                    'retry_attempts': 3
                },
                'monitoring': {
                    'log_level': 'INFO',
                    'save_predictions': True,
                    'alert_on_errors': True,
                    'performance_tracking': True,
                    'daily_reports': True
                },
                'mt5_connection': {
                    'server': 'MetaQuotes-Demo',  # Update with actual server
                    'login': None,  # Set in environment variables
                    'password': None,  # Set in environment variables
                    'timeout': 10000,
                    'retry_connection': True
                }
            },
            
            # ============================================================================
            # FILE PATHS CONFIGURATION
            # ============================================================================
            'file_paths': {
                'data_directory': 'data',
                'models_directory': 'models',
                'logs_directory': 'logs',
                'reports_directory': 'reports',
                'raw_data_file': 'XAU_5m_data.csv',
                'processed_data_file': 'XAU_processed.csv',
                'balanced_dataset_file': 'XAU_balanced_dataset.npz',
                'model_file': 'lstm_gold_model.h5',
                'scaler_file': 'feature_scaler.pkl',
                'config_backup_file': 'config_backup.json'
            },
            
            # ============================================================================
            # VALIDATION AND QUALITY CONTROL
            # ============================================================================
            'validation': {
                'data_quality_thresholds': {
                    'excellent': 0.95,
                    'good': 0.85,
                    'acceptable': 0.70
                },
                'model_validation': {
                    'cross_validation_folds': 5,
                    'walk_forward_periods': 12,
                    'out_of_sample_ratio': 0.2
                },
                'backtesting': {
                    'start_date': '2023-01-01',
                    'end_date': '2024-12-31',
                    'initial_balance': 10000,
                    'commission_per_lot': 5,
                    'spread_pips': 2
                }
            }
        }
    
    def _apply_environment_overrides(self):
        """
        Apply environment-specific configuration overrides.
        """
        if self.environment == "production":
            # Production overrides
            self.config['live_trading']['execution']['update_interval_seconds'] = 30
            self.config['live_trading']['monitoring']['log_level'] = 'WARNING'
            self.config['risk_management']['position_sizing']['risk_per_trade'] = 0.001  # More conservative
            self.config['lstm_model']['performance_targets']['min_accuracy'] = 0.60  # Higher threshold
            
        elif self.environment == "backtesting":
            # Backtesting overrides
            self.config['data_preparation']['years_back'] = 5  # More data for backtesting
            self.config['lstm_model']['training']['epochs'] = 50  # Faster training
            self.config['validation']['backtesting']['initial_balance'] = 100000
            
        elif self.environment == "development":
            # Development overrides (more lenient for testing)
            self.config['lstm_model']['training']['epochs'] = 20  # Faster development cycles
            self.config['lstm_model']['performance_targets']['min_accuracy'] = 0.50
            self.config['live_trading']['monitoring']['log_level'] = 'DEBUG'
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to configuration value
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        Set configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to configuration value
            value: Value to set
        """
        keys = key_path.split('.')
        config_ref = self.config
        
        for key in keys[:-1]:
            if key not in config_ref:
                config_ref[key] = {}
            config_ref = config_ref[key]
        
        config_ref[keys[-1]] = value
    
    def save_config(self, filepath: Optional[str] = None):
        """
        Save current configuration to file.
        
        Args:
            filepath: Optional file path, defaults to config backup file
        """
        if filepath is None:
            filepath = self.get('file_paths.config_backup_file', 'config_backup.json')
        
        config_with_metadata = {
            'environment': self.environment,
            'saved_timestamp': datetime.now().isoformat(),
            'config': self.config
        }
        
        with open(filepath, 'w') as f:
            json.dump(config_with_metadata, f, indent=2, default=str)
    
    def load_config(self, filepath: str):
        """
        Load configuration from file.
        
        Args:
            filepath: Path to configuration file
        """
        with open(filepath, 'r') as f:
            loaded_data = json.load(f)
        
        if 'config' in loaded_data:
            self.config = loaded_data['config']
            self.environment = loaded_data.get('environment', 'development')
        else:
            self.config = loaded_data
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section.
        
        Args:
            section: Section name
            
        Returns:
            Configuration section dictionary
        """
        return self.config.get(section, {})
    
    def validate_config(self) -> Dict[str, Any]:
        """
        Validate configuration for completeness and consistency.
        
        Returns:
            Validation results
        """
        validation_results = {
            'status': 'success',
            'errors': [],
            'warnings': []
        }
        
        # Check required sections
        required_sections = [
            'data_preparation', 'technical_indicators', 'lstm_sequences',
            'target_labels', 'lstm_model', 'risk_management'
        ]
        
        for section in required_sections:
            if section not in self.config:
                validation_results['errors'].append(f"Missing required section: {section}")
        
        # Validate risk management ratios
        risk_per_trade = self.get('risk_management.position_sizing.risk_per_trade', 0)
        if risk_per_trade > 0.05:  # 5%
            validation_results['warnings'].append("Risk per trade is very high (>5%)")
        
        # Validate model performance targets
        min_accuracy = self.get('lstm_model.performance_targets.min_accuracy', 0)
        if min_accuracy < 0.5:
            validation_results['warnings'].append("Minimum accuracy target is below 50%")
        
        # Validate data preparation parameters
        years_back = self.get('data_preparation.years_back', 0)
        if years_back > 10:
            validation_results['warnings'].append("Using more than 10 years of data may introduce bias")
        
        if validation_results['errors']:
            validation_results['status'] = 'error'
        elif validation_results['warnings']:
            validation_results['status'] = 'warning'
        
        return validation_results

# Global configuration instance
_config_instance = None

def get_config(environment: str = "development") -> MasterConfig:
    """
    Get global configuration instance.
    
    Args:
        environment: Environment type
        
    Returns:
        MasterConfig instance
    """
    global _config_instance
    if _config_instance is None or _config_instance.environment != environment:
        _config_instance = MasterConfig(environment)
    return _config_instance

def reload_config(environment: str = "development") -> MasterConfig:
    """
    Force reload of configuration.
    
    Args:
        environment: Environment type
        
    Returns:
        New MasterConfig instance
    """
    global _config_instance
    _config_instance = MasterConfig(environment)
    return _config_instance
