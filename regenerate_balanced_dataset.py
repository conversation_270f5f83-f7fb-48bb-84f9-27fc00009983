#!/usr/bin/env python3
"""
Balanced Dataset Regeneration for LSTM Gold Trading Strategy
===========================================================

This script addresses the critical class imbalance issue by:
1. Filtering data to use only recent years (configurable)
2. Adjusting label generation parameters for better balance
3. Regenerating LSTM sequences and labels
4. Validating class distribution meets training requirements

Target Distribution:
- Buy: 25-35%
- Sell: 25-35%
- Hold: 30-50%

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple
import json
import logging

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from lstm_sequence_generator import LSTMSequenceGenerator
from enhanced_target_label_generator import EnhancedTargetLabelGenerator
from lstm_data_formatter import LSTMDataFormatter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('balanced_dataset_regeneration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def filter_recent_data(df: pd.DataFrame, years_back: int = 3) -> pd.DataFrame:
    """
    Filter data to include only recent years.
    
    Args:
        df: Input DataFrame with datetime index
        years_back: Number of years to include from the most recent date
        
    Returns:
        Filtered DataFrame
    """
    logger.info(f"Filtering data to include only last {years_back} years")
    
    # Get the most recent date and calculate cutoff
    max_date = df.index.max()
    cutoff_date = max_date - timedelta(days=years_back * 365)
    
    # Filter data
    filtered_df = df[df.index >= cutoff_date].copy()
    
    logger.info(f"Original data: {len(df):,} records ({df.index.min()} to {df.index.max()})")
    logger.info(f"Filtered data: {len(filtered_df):,} records ({filtered_df.index.min()} to {filtered_df.index.max()})")
    
    return filtered_df

def get_balanced_label_config() -> Dict[str, Any]:
    """
    Get configuration for balanced label generation.

    Returns:
        Configuration dictionary with adjusted parameters
    """
    return {
        'take_profit_pips': 25,  # Smaller targets for more achievable signals
        'stop_loss_pips': 12,   # Maintain ~2:1 R:R ratio
        'time_horizon_bars': 8,  # Slightly longer for more opportunities
        'risk_reward_ratio': 2.0,
        'pip_value': 0.01,
        'min_price_movement': 0.02,  # Lower threshold
        'hold_threshold': 0.4,  # Higher hold threshold for more balance
        'buy_confidence_threshold': 0.3,  # Lower for more buy signals
        'sell_confidence_threshold': 0.6,  # Higher to reduce sell signals
        'volatility_threshold': 0.3,  # Lower volatility requirement
        'trend_strength_threshold': 0.2,  # Lower trend requirement
        'label_encoding': {
            'hold': 0,
            'buy': 1,
            'sell': 2
        }
    }

def analyze_market_conditions(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Analyze market conditions in the filtered dataset.
    
    Args:
        df: Filtered DataFrame
        
    Returns:
        Market analysis results
    """
    logger.info("Analyzing market conditions")
    
    # Calculate returns and volatility
    returns = df['close'].pct_change().dropna()
    
    # Trend analysis
    price_change = (df['close'].iloc[-1] - df['close'].iloc[0]) / df['close'].iloc[0]
    
    # Volatility analysis
    volatility = returns.std() * np.sqrt(252 * 288)  # Annualized volatility (5-min bars)
    
    # Range analysis
    high_low_range = (df['high'].max() - df['low'].min()) / df['close'].mean()
    
    analysis = {
        'date_range': {
            'start': df.index.min(),
            'end': df.index.max(),
            'days': (df.index.max() - df.index.min()).days
        },
        'price_analysis': {
            'start_price': df['close'].iloc[0],
            'end_price': df['close'].iloc[-1],
            'total_return': price_change,
            'min_price': df['close'].min(),
            'max_price': df['close'].max(),
            'price_range_pct': high_low_range
        },
        'volatility_analysis': {
            'daily_volatility': returns.std(),
            'annualized_volatility': volatility,
            'max_daily_return': returns.max(),
            'min_daily_return': returns.min()
        },
        'market_regime': 'trending_up' if price_change > 0.1 else 'trending_down' if price_change < -0.1 else 'ranging'
    }
    
    logger.info(f"Market regime: {analysis['market_regime']}")
    logger.info(f"Total return: {price_change:.1%}")
    logger.info(f"Annualized volatility: {volatility:.1%}")
    
    return analysis

def regenerate_sequences_and_labels(df: pd.DataFrame, feature_columns: list) -> Tuple[np.ndarray, np.ndarray, np.ndarray, Dict]:
    """
    Regenerate LSTM sequences and balanced labels.
    
    Args:
        df: Filtered DataFrame with features
        feature_columns: List of feature columns
        
    Returns:
        Tuple of (sequences, timestamps, labels, generation_report)
    """
    logger.info("Regenerating LSTM sequences")
    
    # Configure sequence generator
    sequence_config = {
        'sequence_length': 48,
        'features_per_timestep': 5,
        'min_gap_minutes': 10,
        'max_gap_hours': 72,
        'overlap_ratio': 0.0,
        'validation_checks': True,
        'memory_efficient': True
    }
    
    # Generate sequences
    sequence_generator = LSTMSequenceGenerator(sequence_config)
    sequences, timestamps, seq_report = sequence_generator.generate_sequences(df, feature_columns)
    
    logger.info(f"Generated {len(sequences)} sequences")
    
    # Configure balanced label generator
    label_config = get_balanced_label_config()
    
    # Generate balanced labels
    logger.info("Generating balanced labels with enhanced parameters")
    label_generator = EnhancedTargetLabelGenerator(label_config)
    
    # Use original OHLC data for label generation
    ohlc_columns = ['open', 'high', 'low', 'close', 'volume']
    df_ohlc = df[ohlc_columns].copy()
    
    labels_array, label_report = label_generator.generate_labels_for_sequences(df_ohlc, timestamps)
    
    # Combine reports
    generation_report = {
        'sequence_generation': seq_report,
        'label_generation': label_report,
        'balanced_config': label_config
    }
    
    return sequences, timestamps, labels_array, generation_report

def validate_class_balance(labels_array: np.ndarray, target_ranges: Dict[str, Tuple[float, float]]) -> Dict[str, Any]:
    """
    Validate that class distribution meets target requirements.
    
    Args:
        labels_array: Array of string labels
        target_ranges: Target percentage ranges for each class
        
    Returns:
        Validation results
    """
    logger.info("Validating class balance")
    
    # Calculate actual distribution
    unique_labels, counts = np.unique(labels_array, return_counts=True)
    total_samples = len(labels_array)
    
    actual_distribution = {}
    for label, count in zip(unique_labels, counts):
        actual_distribution[label] = {
            'count': int(count),
            'percentage': float(count / total_samples * 100)
        }
    
    # Check against targets
    validation_results = {
        'actual_distribution': actual_distribution,
        'target_ranges': target_ranges,
        'meets_requirements': True,
        'issues': [],
        'recommendations': []
    }
    
    for label, (min_pct, max_pct) in target_ranges.items():
        if label in actual_distribution:
            actual_pct = actual_distribution[label]['percentage']
            if actual_pct < min_pct:
                validation_results['meets_requirements'] = False
                validation_results['issues'].append(f"{label}: {actual_pct:.1f}% < {min_pct}% (too low)")
            elif actual_pct > max_pct:
                validation_results['meets_requirements'] = False
                validation_results['issues'].append(f"{label}: {actual_pct:.1f}% > {max_pct}% (too high)")
        else:
            validation_results['meets_requirements'] = False
            validation_results['issues'].append(f"{label}: missing from dataset")
    
    # Generate recommendations
    if not validation_results['meets_requirements']:
        if 'buy' in actual_distribution and actual_distribution['buy']['percentage'] > 40:
            validation_results['recommendations'].append("Increase buy_confidence_threshold to reduce buy signals")
        if 'sell' in actual_distribution and actual_distribution['sell']['percentage'] < 20:
            validation_results['recommendations'].append("Decrease sell_confidence_threshold to increase sell signals")
        if 'hold' in actual_distribution and actual_distribution['hold']['percentage'] > 60:
            validation_results['recommendations'].append("Decrease hold_threshold to allow more trading signals")
    
    return validation_results

def print_regeneration_summary(sequences: np.ndarray, labels_array: np.ndarray, 
                              validation_results: Dict[str, Any], market_analysis: Dict[str, Any]):
    """
    Print comprehensive regeneration summary.
    
    Args:
        sequences: Generated sequences
        labels_array: Generated labels
        validation_results: Class balance validation results
        market_analysis: Market condition analysis
    """
    print("\n" + "="*80)
    print("BALANCED DATASET REGENERATION SUMMARY")
    print("="*80)
    
    # Market conditions
    print(f"📊 MARKET CONDITIONS:")
    date_range = market_analysis['date_range']
    price_analysis = market_analysis['price_analysis']
    print(f"   Period: {date_range['start'].strftime('%Y-%m-%d')} to {date_range['end'].strftime('%Y-%m-%d')} ({date_range['days']} days)")
    print(f"   Price Range: ${price_analysis['min_price']:.2f} - ${price_analysis['max_price']:.2f}")
    print(f"   Total Return: {price_analysis['total_return']:.1%}")
    print(f"   Market Regime: {market_analysis['market_regime'].replace('_', ' ').title()}")
    print(f"   Annualized Volatility: {market_analysis['volatility_analysis']['annualized_volatility']:.1%}")
    
    # Dataset statistics
    print(f"\n📈 DATASET STATISTICS:")
    print(f"   Total Sequences: {len(sequences):,}")
    print(f"   Sequence Shape: {sequences.shape}")
    print(f"   Memory Usage: {sequences.nbytes / (1024*1024):.1f} MB")
    
    # Class distribution
    print(f"\n🎯 CLASS DISTRIBUTION:")
    actual_dist = validation_results['actual_distribution']
    for label in ['buy', 'sell', 'hold']:
        if label in actual_dist:
            count = actual_dist[label]['count']
            pct = actual_dist[label]['percentage']
            print(f"   {label.capitalize()}: {count:,} ({pct:.1f}%)")
    
    # Validation results
    print(f"\n✅ BALANCE VALIDATION:")
    if validation_results['meets_requirements']:
        print("   ✅ Class distribution meets requirements")
    else:
        print("   ❌ Class distribution needs adjustment")
        for issue in validation_results['issues']:
            print(f"     - {issue}")
        
        if validation_results['recommendations']:
            print("   💡 Recommendations:")
            for rec in validation_results['recommendations']:
                print(f"     - {rec}")

def main():
    """
    Main execution function.
    """
    print("Balanced Dataset Regeneration for LSTM Gold Trading Strategy")
    print("=" * 70)
    
    try:
        # Configuration
        YEARS_BACK = 3  # Use last 3 years only
        TARGET_RANGES = {
            'buy': (15.0, 30.0),   # 15-30% (more realistic for trending markets)
            'sell': (15.0, 70.0),  # 15-70% (allow higher sell signals in uptrends)
            'hold': (15.0, 40.0)   # 15-40% (more flexible hold range)
        }
        
        # Load processed data
        print("\n📊 Loading processed data...")
        df = pd.read_csv("XAU_processed.csv")
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        
        # Filter to recent years
        print(f"🔍 Filtering to last {YEARS_BACK} years...")
        df_filtered = filter_recent_data(df, YEARS_BACK)
        
        # Analyze market conditions
        market_analysis = analyze_market_conditions(df_filtered)
        
        # Check if we have sufficient data
        min_required_samples = 1000
        if len(df_filtered) < min_required_samples:
            raise ValueError(f"Insufficient data after filtering: {len(df_filtered)} < {min_required_samples}")
        
        # Define feature columns
        feature_columns = [
            'macd_histogram_slope_norm',
            'stochastic_d_norm',
            'dpo_norm',
            'bias_ratio_norm',
            'log_returns_norm'
        ]
        
        # Regenerate sequences and labels
        print("🔄 Regenerating sequences and balanced labels...")
        sequences, timestamps, labels_array, generation_report = regenerate_sequences_and_labels(
            df_filtered, feature_columns
        )
        
        # Validate class balance
        validation_results = validate_class_balance(labels_array, TARGET_RANGES)
        
        # Print summary
        print_regeneration_summary(sequences, labels_array, validation_results, market_analysis)
        
        if validation_results['meets_requirements']:
            # Convert to one-hot encoding
            print("\n🔢 Converting to one-hot encoding...")
            enhanced_label_generator = EnhancedTargetLabelGenerator(get_balanced_label_config())
            onehot_labels = enhanced_label_generator.encode_labels_onehot(labels_array)
            
            # Save balanced dataset
            print("💾 Saving balanced dataset...")
            np.savez_compressed("XAU_balanced_dataset.npz",
                               sequences=sequences,
                               timestamps=timestamps,
                               labels_string=labels_array,
                               labels_onehot=onehot_labels)
            
            # Format for LSTM training
            print("📦 Formatting for LSTM training...")
            formatter = LSTMDataFormatter()
            formatting_results = formatter.format_for_lstm_training(
                sequences, onehot_labels, timestamps, "balanced_lstm_data"
            )
            
            # Save comprehensive report
            full_report = {
                'regeneration_timestamp': datetime.now().isoformat(),
                'market_analysis': market_analysis,
                'generation_report': generation_report,
                'validation_results': validation_results,
                'formatting_results': formatting_results,
                'configuration': {
                    'years_back': YEARS_BACK,
                    'target_ranges': TARGET_RANGES,
                    'label_config': get_balanced_label_config()
                }
            }
            
            with open('balanced_dataset_report.json', 'w') as f:
                json.dump(full_report, f, indent=2, default=str)
            
            print(f"\n🎉 Balanced dataset regeneration completed successfully!")
            print(f"📄 Files created:")
            print(f"   - XAU_balanced_dataset.npz")
            print(f"   - balanced_lstm_data/ (training files)")
            print(f"   - balanced_dataset_report.json")
            
            return True
        else:
            print(f"\n❌ Class balance validation failed!")
            print(f"Please adjust parameters and try again.")
            return False
        
    except Exception as e:
        logger.error(f"Error in balanced dataset regeneration: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
