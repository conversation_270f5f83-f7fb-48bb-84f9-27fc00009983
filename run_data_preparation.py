#!/usr/bin/env python3
"""
XAUUSD Data Preparation Pipeline Runner
======================================

This script runs the comprehensive data preparation pipeline for XAUUSD 5-minute data.
It handles data validation, gap analysis, MT5 integration, and quality control.

Usage:
    python run_data_preparation.py

Requirements:
    - XAU_5m_data.csv file should be present in the current directory
    - MetaTrader5 should be installed and logged in (optional, for data updates)
    - All required packages should be installed in the virtual environment

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from data_preparation_pipeline import XAUDataPreparationPipeline, print_summary_report

def check_prerequisites():
    """
    Check if all prerequisites are met before running the pipeline.
    
    Returns:
        bool: True if all prerequisites are met, False otherwise
    """
    print("Checking prerequisites...")
    
    # Check if CSV file exists
    csv_file = "XAU_5m_data.csv"
    if not os.path.exists(csv_file):
        print(f"❌ Error: {csv_file} not found in current directory")
        print("   Please ensure you have downloaded the XAUUSD data from Kaggle")
        return False
    else:
        print(f"✅ Found {csv_file}")
    
    # Check if required packages are available
    try:
        import pandas as pd
        import numpy as np
        import MetaTrader5 as mt5
        import pytz
        print("✅ All required packages are available")
    except ImportError as e:
        print(f"❌ Error: Missing required package - {e}")
        print("   Please ensure all packages are installed in your virtual environment")
        return False
    
    print("✅ All prerequisites met")
    return True

def main():
    """
    Main function to run the data preparation pipeline.
    """
    print("XAUUSD Data Preparation Pipeline")
    print("=" * 50)
    print()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease resolve the issues above before running the pipeline.")
        return False
    
    print("\nStarting data preparation pipeline...")
    print("-" * 50)
    
    try:
        # Initialize and run pipeline
        pipeline = XAUDataPreparationPipeline("XAU_5m_data.csv")
        report = pipeline.run_pipeline()
        
        # Print results
        print_summary_report(report)
        
        if report.get('success', False):
            print("\n🎉 Data preparation completed successfully!")
            print("\nNext steps:")
            print("1. Review the data quality report above")
            print("2. Check the processed XAU_5m_data.csv file")
            print("3. Proceed with technical indicator calculations")
            return True
        else:
            print(f"\n❌ Pipeline failed: {report.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
