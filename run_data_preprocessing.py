#!/usr/bin/env python3
"""
Data Preprocessing Runner for LSTM Gold Trading Strategy
=======================================================

This script runs advanced data preprocessing on the calculated technical indicators.
It handles normalization, outlier detection, missing values, and feature engineering.

Usage:
    python run_data_preprocessing.py

Requirements:
    - XAU_features.csv (output from technical indicators calculation)
    - All required packages installed in virtual environment

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
import json
import logging

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from data_preprocessing import DataPreprocessor
from config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_preprocessing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """
    Check if all prerequisites are met.
    
    Returns:
        bool: True if all prerequisites are met
    """
    print("Checking prerequisites...")
    
    # Check if features data exists
    features_file = "XAU_features.csv"
    if not os.path.exists(features_file):
        print(f"❌ Error: {features_file} not found")
        print("   Please run the technical indicators calculation first")
        return False
    else:
        print(f"✅ Found {features_file}")
    
    # Check required packages
    try:
        import pandas as pd
        import numpy as np
        import sklearn
        from scipy import stats
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Error: Missing package - {e}")
        return False
    
    return True

def load_features_data(filepath: str) -> pd.DataFrame:
    """
    Load features data with technical indicators.
    
    Args:
        filepath: Path to CSV file
        
    Returns:
        DataFrame with features
    """
    logger.info(f"Loading features data from {filepath}")
    
    df = pd.read_csv(filepath)
    
    # Convert datetime column
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    
    logger.info(f"Loaded {len(df)} records")
    logger.info(f"Columns: {list(df.columns)}")
    
    return df

def print_preprocessing_summary(report: dict):
    """
    Print formatted preprocessing summary.
    
    Args:
        report: Processing report dictionary
    """
    print("\n" + "="*80)
    print("DATA PREPROCESSING SUMMARY")
    print("="*80)
    
    # Shape changes
    original_shape = report.get('original_shape', (0, 0))
    final_shape = report.get('final_shape', (0, 0))
    shape_change = report.get('shape_change', {})
    
    print(f"📊 Dataset Shape: {original_shape} → {final_shape}")
    print(f"📈 Rows Change: {shape_change.get('rows_change', 0):+d}")
    print(f"📈 Columns Change: {shape_change.get('cols_change', 0):+d}")
    
    # Steps completed
    steps = report.get('steps_completed', [])
    print(f"\n✅ Processing Steps Completed ({len(steps)}):")
    for i, step in enumerate(steps, 1):
        print(f"  {i}. {step.replace('_', ' ').title()}")
    
    # Missing values handling
    missing_stats = report.get('processing_stats', {}).get('missing_values', {})
    if missing_stats:
        original_missing = missing_stats.get('original_missing', 0)
        final_missing = missing_stats.get('final_missing', 0)
        print(f"\n🔧 Missing Values: {original_missing} → {final_missing}")
    
    # Validation results
    validation = report.get('validation_report', {})
    if validation:
        print(f"\n🔍 Data Quality: {validation.get('overall_quality', 'unknown').upper()}")
        
        issues = validation.get('issues', [])
        if issues:
            print(f"⚠️  Issues Found ({len(issues)}):")
            for issue in issues[:5]:  # Show first 5 issues
                print(f"    - {issue}")
            if len(issues) > 5:
                print(f"    ... and {len(issues) - 5} more")
        
        recommendations = validation.get('recommendations', [])
        if recommendations:
            print(f"💡 Recommendations ({len(recommendations)}):")
            for rec in recommendations[:3]:  # Show first 3 recommendations
                print(f"    - {rec}")
            if len(recommendations) > 3:
                print(f"    ... and {len(recommendations) - 3} more")

def print_feature_statistics(df: pd.DataFrame, feature_columns: list):
    """
    Print feature statistics summary.
    
    Args:
        df: DataFrame with processed features
        feature_columns: List of feature columns
    """
    print("\n" + "="*80)
    print("PROCESSED FEATURES STATISTICS")
    print("="*80)
    
    print(f"📊 Total Features: {len(feature_columns)}")
    print(f"📈 Total Records: {len(df):,}")
    
    # Calculate completeness
    completeness_stats = {}
    for col in feature_columns:
        if col in df.columns:
            completeness = df[col].notna().sum() / len(df)
            completeness_stats[col] = completeness
    
    avg_completeness = np.mean(list(completeness_stats.values()))
    print(f"📋 Average Completeness: {avg_completeness:.1%}")
    
    # Show feature ranges
    print(f"\n🔢 FEATURE RANGES:")
    print("-" * 80)
    
    for col in feature_columns:
        if col in df.columns:
            series = df[col]
            print(f"{col:30} [{series.min():8.4f}, {series.max():8.4f}] "
                  f"μ={series.mean():7.4f} σ={series.std():7.4f}")

def save_processed_data(df: pd.DataFrame, output_path: str = "XAU_processed.csv"):
    """
    Save processed data to CSV file.
    
    Args:
        df: Processed DataFrame
        output_path: Output file path
    """
    logger.info(f"Saving processed data to {output_path}")
    
    # Reset index to save datetime as column
    df_save = df.reset_index()
    
    # Convert datetime to string for CSV compatibility
    df_save['datetime'] = df_save['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Save to CSV
    df_save.to_csv(output_path, index=False)
    
    logger.info(f"Processed data saved: {len(df_save)} records to {output_path}")

def main():
    """
    Main execution function.
    """
    print("Data Preprocessing for LSTM Gold Trading Strategy")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease resolve issues above before continuing.")
        return False
    
    try:
        # Load configuration with proper defaults
        preprocessing_config = {
            'normalization': {
                'method': 'minmax',
                'feature_range': (-1, 1),
                'clip_outliers': True,
                'outlier_std_threshold': 3.0
            },
            'missing_values': {
                'strategy': 'interpolate',
                'max_missing_ratio': 0.05,
                'interpolation_method': 'linear'
            },
            'outlier_detection': {
                'method': 'iqr',
                'iqr_multiplier': 1.5,
                'zscore_threshold': 3.0,
                'handle_outliers': 'clip'
            },
            'feature_engineering': {
                'add_rolling_stats': False,  # Keep simple for LSTM
                'rolling_windows': [5, 10, 20],
                'add_lag_features': False,
                'lag_periods': [1, 2, 3]
            }
        }
        
        logger.info("Configuration loaded")
        
        # Load features data
        df = load_features_data("XAU_features.csv")
        
        # Define feature columns (normalized indicators)
        feature_columns = [
            'macd_histogram_slope_norm',
            'stochastic_d_norm',
            'dpo_norm',
            'bias_ratio_norm',
            'log_returns_norm'
        ]
        
        # Verify feature columns exist
        missing_features = [col for col in feature_columns if col not in df.columns]
        if missing_features:
            logger.error(f"Missing feature columns: {missing_features}")
            return False
        
        # Initialize preprocessor
        print("\n🔧 Initializing data preprocessor...")
        preprocessor = DataPreprocessor(preprocessing_config)
        
        # Process features
        print("🔄 Processing features...")
        print("   - Handling missing values")
        print("   - Detecting and handling outliers")
        print("   - Validating data quality")
        
        df_processed, processing_report = preprocessor.process_features(df, feature_columns)
        
        # Print results
        print_preprocessing_summary(processing_report)
        print_feature_statistics(df_processed, feature_columns)
        
        # Save results
        print("\n💾 Saving results...")
        save_processed_data(df_processed, "XAU_processed.csv")
        
        # Save preprocessing objects
        preprocessor.save_preprocessing_objects("preprocessing_objects.pkl")
        
        # Save detailed report
        with open('data_preprocessing_report.json', 'w') as f:
            json.dump(processing_report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: data_preprocessing_report.json")
        print(f"🔧 Preprocessing objects saved to: preprocessing_objects.pkl")
        
        # Final summary
        valid_records = df_processed[feature_columns].dropna()
        
        print(f"\n🎉 Data preprocessing completed successfully!")
        print(f"📊 Processed records: {len(df_processed):,}")
        print(f"📈 Valid feature records: {len(valid_records):,}")
        print(f"💾 Output file: XAU_processed.csv")
        
        print(f"\nNext steps:")
        print("1. Review the preprocessing report above")
        print("2. Check XAU_processed.csv for processed data")
        print("3. Proceed with LSTM sequence generation")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in data preprocessing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
