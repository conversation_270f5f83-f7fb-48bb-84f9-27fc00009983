#!/usr/bin/env python3
"""
Data Quality Validation Runner for LSTM Gold Trading Strategy
============================================================

This script runs comprehensive data quality validation on the complete dataset
including OHLC data, sequences, and labels. It generates detailed quality reports
and recommendations for LSTM training readiness.

Usage:
    python run_data_quality_validation.py

Requirements:
    - XAU_processed.csv (processed OHLC data)
    - XAU_dataset_with_labels.npz (sequences with labels)
    - All required packages installed in virtual environment

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any
import json
import logging

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from data_quality_validator import DataQualityValidator
from config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_quality_validation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """
    Check if all prerequisites are met.
    
    Returns:
        bool: True if all prerequisites are met
    """
    print("Checking prerequisites...")
    
    # Check if processed data exists
    processed_file = "XAU_processed.csv"
    if not os.path.exists(processed_file):
        print(f"❌ Error: {processed_file} not found")
        print("   Please run the data preprocessing first")
        return False
    else:
        print(f"✅ Found {processed_file}")
    
    # Check if dataset with labels exists
    dataset_file = "XAU_dataset_with_labels.npz"
    if not os.path.exists(dataset_file):
        print(f"❌ Error: {dataset_file} not found")
        print("   Please run the target label generation first")
        return False
    else:
        print(f"✅ Found {dataset_file}")
    
    # Check required packages
    try:
        import pandas as pd
        import numpy as np
        from scipy import stats
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Error: Missing package - {e}")
        return False
    
    return True

def load_processed_data(filepath: str) -> pd.DataFrame:
    """
    Load processed OHLC data.
    
    Args:
        filepath: Path to CSV file
        
    Returns:
        DataFrame with OHLC data
    """
    logger.info(f"Loading processed data from {filepath}")
    
    df = pd.read_csv(filepath)
    
    # Convert datetime column
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    
    logger.info(f"Loaded {len(df)} OHLC records")
    return df

def load_dataset_with_labels(filepath: str) -> tuple:
    """
    Load complete dataset with sequences and labels.
    
    Args:
        filepath: Path to dataset file
        
    Returns:
        Tuple of (sequences, timestamps, labels_string, labels_onehot)
    """
    logger.info(f"Loading dataset from {filepath}")
    
    data = np.load(filepath, allow_pickle=True)
    
    sequences = data['sequences']
    timestamps = data['timestamps']
    labels_string = data['labels_string']
    labels_onehot = data['labels_onehot']
    
    logger.info(f"Loaded dataset: {sequences.shape} sequences, {labels_onehot.shape} labels")
    return sequences, timestamps, labels_string, labels_onehot

def print_validation_summary(report: Dict[str, Any]):
    """
    Print formatted validation summary.
    
    Args:
        report: Comprehensive validation report
    """
    print("\n" + "="*80)
    print("DATA QUALITY VALIDATION SUMMARY")
    print("="*80)
    
    # Overall quality
    overall_score = report.get('overall_quality_score', 0)
    quality_level = report.get('quality_level', 'unknown')
    
    print(f"🎯 OVERALL QUALITY: {quality_level.upper()} ({overall_score:.3f})")
    
    # Quality level interpretation
    if quality_level == 'excellent':
        print("   ✅ Data exceeds all quality standards")
    elif quality_level == 'good':
        print("   ✅ Data meets high quality standards")
    elif quality_level == 'acceptable':
        print("   ⚠️  Data meets minimum standards but has room for improvement")
    elif quality_level == 'poor':
        print("   ⚠️  Data has significant quality issues")
    else:
        print("   ❌ Data has critical quality issues")
    
    # Summary statistics
    summary = report.get('summary', {})
    print(f"\n📊 VALIDATION SUMMARY:")
    print(f"   Total Validations: {summary.get('total_validations', 0)}")
    print(f"   Passed: {summary.get('passed_validations', 0)} ✅")
    print(f"   Warnings: {summary.get('warning_validations', 0)} ⚠️")
    print(f"   Failed: {summary.get('failed_validations', 0)} ❌")
    print(f"   LSTM Ready: {'Yes' if summary.get('lstm_ready', False) else 'No'} {'✅' if summary.get('lstm_ready', False) else '❌'}")
    
    # Individual validation results
    individual = report.get('individual_validations', {})
    print(f"\n🔍 INDIVIDUAL VALIDATIONS:")
    
    for validation_name, result in individual.items():
        status = result.get('status', 'unknown')
        score = result.get('score', 0)
        message = result.get('message', 'No message')
        
        status_icon = {'good': '✅', 'warning': '⚠️', 'error': '❌'}.get(status, '❓')
        
        print(f"   {validation_name.replace('_', ' ').title()}: {status_icon} {score:.3f}")
        print(f"     {message}")
    
    # Critical issues
    issues = report.get('critical_issues', [])
    if issues:
        print(f"\n❌ CRITICAL ISSUES ({len(issues)}):")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    
    # Recommendations
    recommendations = report.get('recommendations', [])
    if recommendations:
        print(f"\n💡 RECOMMENDATIONS ({len(recommendations)}):")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")
    
    # LSTM Training Readiness
    lstm_ready = summary.get('lstm_ready', False)
    print(f"\n🚀 LSTM TRAINING READINESS:")
    if lstm_ready:
        print("   ✅ Data is ready for LSTM training")
        print("   ✅ All critical issues resolved")
        print("   ✅ Quality score meets minimum requirements")
    else:
        print("   ❌ Data is NOT ready for LSTM training")
        if len(issues) > 0:
            print("   ❌ Critical issues must be resolved first")
        if overall_score < 0.70:
            print("   ❌ Quality score below minimum threshold (0.70)")

def print_detailed_statistics(report: Dict[str, Any]):
    """
    Print detailed statistics from validation.
    
    Args:
        report: Comprehensive validation report
    """
    print("\n" + "="*80)
    print("DETAILED VALIDATION STATISTICS")
    print("="*80)
    
    individual = report.get('individual_validations', {})
    
    # Trading hours statistics
    trading_hours = individual.get('trading_hours', {})
    if trading_hours:
        print(f"\n📅 TRADING HOURS:")
        print(f"   Total Records: {trading_hours.get('total_records', 0):,}")
        print(f"   Trading Hours Records: {trading_hours.get('trading_hours_records', 0):,}")
        print(f"   Coverage Ratio: {trading_hours.get('coverage_ratio', 0):.1%}")
        print(f"   Outside Hours: {trading_hours.get('outside_hours_records', 0):,}")
        print(f"   Weekend Records: {trading_hours.get('weekend_records', 0):,}")
    
    # Volume statistics
    volume_data = individual.get('volume_data', {})
    if volume_data:
        print(f"\n📊 VOLUME DATA:")
        print(f"   Zero Volume Count: {volume_data.get('zero_volume_count', 0):,}")
        print(f"   Zero Volume Ratio: {volume_data.get('zero_volume_ratio', 0):.1%}")
        print(f"   Volume Spikes: {volume_data.get('volume_spikes', 0):,}")
        print(f"   Mean Volume: {volume_data.get('mean_volume', 0):,.0f}")
        print(f"   Volume Range: [{volume_data.get('min_volume', 0):,.0f}, {volume_data.get('max_volume', 0):,.0f}]")
    
    # Price statistics
    price_data = individual.get('price_data', {})
    if price_data:
        print(f"\n💰 PRICE DATA:")
        print(f"   OHLC Issues: {price_data.get('ohlc_consistency_issues', 0):,}")
        print(f"   Price Spikes: {price_data.get('price_spikes', 0):,}")
        print(f"   Zero Movement Bars: {price_data.get('zero_movement_bars', 0):,}")
        print(f"   Missing Values: {price_data.get('missing_values', 0):,}")
        
        price_stats = price_data.get('price_statistics', {})
        if price_stats:
            print(f"   Price Range: [{price_stats.get('min_close', 0):.2f}, {price_stats.get('max_close', 0):.2f}]")
            print(f"   Mean Close: {price_stats.get('mean_close', 0):.2f}")
            print(f"   Mean Range: {price_stats.get('mean_range', 0):.2f}")
    
    # Sequence quality statistics
    sequence_quality = individual.get('sequence_quality', {})
    if sequence_quality:
        print(f"\n🔢 SEQUENCE QUALITY:")
        print(f"   Total Sequences: {sequence_quality.get('total_sequences', 0):,}")
        print(f"   Sequence Shape: {sequence_quality.get('sequence_shape', 'N/A')}")
        print(f"   Finite Ratio: {sequence_quality.get('finite_ratio', 0):.1%}")
        print(f"   Max Feature Correlation: {sequence_quality.get('max_feature_correlation', 0):.3f}")
        print(f"   Min Class Ratio: {sequence_quality.get('min_class_ratio', 0):.1%}")
        
        label_dist = sequence_quality.get('label_distribution', {})
        if label_dist:
            print(f"   Label Distribution:")
            for label, count in label_dist.items():
                print(f"     {label}: {count:,}")

def main():
    """
    Main execution function.
    """
    print("Data Quality Validation for LSTM Gold Trading Strategy")
    print("=" * 65)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease resolve issues above before continuing.")
        return False
    
    try:
        # Load configuration
        config = get_config()
        validation_config = {
            'trading_hours': {
                'start_hour': 9,
                'start_minute': 30,
                'end_hour': 17,
                'end_minute': 0,
                'timezone': 'America/New_York'
            },
            'volume_checks': {
                'min_volume_threshold': 0,
                'zero_volume_tolerance': 0.05,
                'volume_spike_threshold': 10.0
            },
            'price_checks': {
                'max_price_change_pct': 5.0,
                'min_price_movement': 0.01,
                'ohlc_consistency_tolerance': 0.001,
                'price_spike_threshold': 3.0
            },
            'data_completeness': {
                'min_samples_required': 1000,
                'max_missing_ratio': 0.01,
                'min_date_coverage_days': 30,
                'required_features': ['open', 'high', 'low', 'close', 'volume']
            },
            'sequence_validation': {
                'min_sequences_required': 1000,
                'label_balance_threshold': 0.02,  # 2% minimum for each class
                'feature_correlation_threshold': 0.95
            },
            'quality_thresholds': {
                'excellent': 0.95,
                'good': 0.85,
                'acceptable': 0.70,
                'poor': 0.50
            }
        }
        
        logger.info("Configuration loaded")
        
        # Load data
        print("\n📊 Loading data for validation...")
        df = load_processed_data("XAU_processed.csv")
        sequences, timestamps, labels_string, labels_onehot = load_dataset_with_labels("XAU_dataset_with_labels.npz")
        
        # Initialize validator
        print("🔧 Initializing data quality validator...")
        validator = DataQualityValidator(validation_config)
        
        # Run comprehensive validation
        print("🔄 Running comprehensive data quality validation...")
        print("   - Trading hours coverage")
        print("   - Volume data quality")
        print("   - Price data consistency")
        print("   - Data completeness")
        print("   - Sequence and label quality")
        
        validation_report = validator.run_comprehensive_validation(df, sequences, labels_string)
        
        # Print results
        print_validation_summary(validation_report)
        print_detailed_statistics(validation_report)
        
        # Save comprehensive report
        validator.generate_quality_report("data_quality_report.json")
        
        # Save summary report
        quality_summary = validator.get_quality_summary()
        with open('data_quality_summary.json', 'w') as f:
            json.dump(quality_summary, f, indent=2, default=str)
        
        print(f"\n📄 Reports saved:")
        print(f"   - data_quality_report.json (comprehensive)")
        print(f"   - data_quality_summary.json (summary)")
        
        # Final assessment
        lstm_ready = validator.is_lstm_ready()
        overall_score = validator.quality_score
        
        print(f"\n🎉 Data quality validation completed!")
        print(f"📊 Overall Quality Score: {overall_score:.3f}")
        print(f"🚀 LSTM Training Ready: {'Yes' if lstm_ready else 'No'}")
        
        if lstm_ready:
            print(f"\nNext steps:")
            print("1. Proceed with LSTM model training")
            print("2. Use the validated dataset for training")
            print("3. Monitor model performance metrics")
        else:
            print(f"\nRequired actions before LSTM training:")
            for issue in validator.critical_issues:
                print(f"   - {issue}")
            for rec in validator.recommendations[:3]:  # Show top 3 recommendations
                print(f"   - {rec}")
        
        return lstm_ready
        
    except Exception as e:
        logger.error(f"Error in data quality validation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
