#!/usr/bin/env python3
"""
Final Data Export Runner for LSTM Gold Trading Strategy
======================================================

This script performs final data formatting and export for LSTM training.
It creates properly formatted X_train/y_train datasets with temporal splits
and exports to multiple formats (.npz, HDF5) with comprehensive metadata.

Usage:
    python run_final_data_export.py

Requirements:
    - XAU_dataset_with_labels.npz (complete dataset)
    - All required packages installed in virtual environment

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any
import json
import logging

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from lstm_data_formatter import LSTMDataFormatter
from config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('final_data_export.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """
    Check if all prerequisites are met.
    
    Returns:
        bool: True if all prerequisites are met
    """
    print("Checking prerequisites...")
    
    # Check if dataset with labels exists
    dataset_file = "XAU_dataset_with_labels.npz"
    if not os.path.exists(dataset_file):
        print(f"❌ Error: {dataset_file} not found")
        print("   Please run the target label generation first")
        return False
    else:
        print(f"✅ Found {dataset_file}")
    
    # Check required packages
    try:
        import pandas as pd
        import numpy as np
        import h5py
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Error: Missing package - {e}")
        return False
    
    return True

def load_complete_dataset(filepath: str) -> tuple:
    """
    Load complete dataset with sequences and labels.
    
    Args:
        filepath: Path to dataset file
        
    Returns:
        Tuple of (sequences, timestamps, labels_string, labels_onehot)
    """
    logger.info(f"Loading complete dataset from {filepath}")
    
    data = np.load(filepath, allow_pickle=True)
    
    sequences = data['sequences']
    timestamps = data['timestamps']
    labels_string = data['labels_string']
    labels_onehot = data['labels_onehot']
    
    logger.info(f"Loaded dataset: {sequences.shape} sequences, {labels_onehot.shape} labels")
    logger.info(f"Date range: {timestamps.min()} to {timestamps.max()}")
    
    return sequences, timestamps, labels_string, labels_onehot

def print_formatting_summary(results: Dict[str, Any]):
    """
    Print formatted data export summary.
    
    Args:
        results: Formatting results dictionary
    """
    print("\n" + "="*80)
    print("FINAL DATA EXPORT SUMMARY")
    print("="*80)
    
    # Status
    status = results.get('status', 'unknown')
    print(f"🎯 EXPORT STATUS: {status.upper()}")
    
    # Input validation
    input_val = results.get('input_validation', {})
    print(f"\n📊 INPUT VALIDATION:")
    print(f"   Status: {input_val.get('status', 'unknown').upper()}")
    
    data_info = input_val.get('data_info', {})
    if data_info:
        print(f"   Sequences Shape: {data_info.get('sequences_shape', 'N/A')}")
        print(f"   Labels Shape: {data_info.get('labels_shape', 'N/A')}")
        print(f"   Memory Usage: {data_info.get('memory_usage_mb', 0):.1f} MB")
    
    # Split validation
    split_val = results.get('split_validation', {})
    print(f"\n🔄 SPLIT VALIDATION:")
    print(f"   Status: {split_val.get('status', 'unknown').upper()}")
    
    split_analysis = split_val.get('split_analysis', {})
    for split_name, analysis in split_analysis.items():
        print(f"   {split_name.capitalize()}:")
        print(f"     Samples: {analysis.get('sample_count', 0):,}")
        print(f"     X Shape: {analysis.get('X_shape', 'N/A')}")
        print(f"     y Shape: {analysis.get('y_shape', 'N/A')}")
        print(f"     Memory: {analysis.get('memory_mb', 0):.1f} MB")
        print(f"     Temporal Span: {analysis.get('temporal_span_days', 0)} days")
        
        # Class distribution if available
        class_dist = analysis.get('class_distribution')
        if class_dist:
            print(f"     Class Distribution: {class_dist}")
    
    # Exported files
    exported_files = results.get('exported_files', {})
    print(f"\n💾 EXPORTED FILES:")
    for file_type, filepath in exported_files.items():
        if file_type != 'metadata':
            file_size = 0
            try:
                file_size = Path(filepath).stat().st_size / (1024 * 1024)
            except:
                pass
            print(f"   {file_type}: {Path(filepath).name} ({file_size:.1f} MB)")
    
    if 'metadata' in exported_files:
        print(f"   metadata: {Path(exported_files['metadata']).name}")

def print_training_recommendations(metadata: Dict[str, Any]):
    """
    Print training recommendations based on data characteristics.
    
    Args:
        metadata: Training metadata dictionary
    """
    print("\n" + "="*80)
    print("LSTM TRAINING RECOMMENDATIONS")
    print("="*80)
    
    dataset_info = metadata.get('dataset_info', {})
    recommended_params = metadata.get('recommended_training_params', {})
    
    print(f"📊 DATASET CHARACTERISTICS:")
    print(f"   Total Samples: {dataset_info.get('total_samples', 0):,}")
    print(f"   Sequence Length: {dataset_info.get('sequence_length', 0)} timesteps")
    print(f"   Feature Count: {dataset_info.get('feature_count', 0)}")
    print(f"   Number of Classes: {dataset_info.get('num_classes', 0)}")
    print(f"   Total Memory: {dataset_info.get('total_memory_mb', 0):.1f} MB")
    
    print(f"\n⚙️  RECOMMENDED TRAINING PARAMETERS:")
    print(f"   Batch Size: {recommended_params.get('batch_size', 32)}")
    print(f"   Epochs: {recommended_params.get('epochs', 100)}")
    print(f"   Learning Rate: {recommended_params.get('learning_rate', 0.001)}")
    print(f"   Early Stopping Patience: {recommended_params.get('early_stopping_patience', 10)}")
    print(f"   Shuffle: {recommended_params.get('shuffle', False)}")
    
    print(f"\n🏗️  SUGGESTED LSTM ARCHITECTURE:")
    feature_count = dataset_info.get('feature_count', 5)
    sequence_length = dataset_info.get('sequence_length', 48)
    num_classes = dataset_info.get('num_classes', 3)
    
    print(f"   Input Layer: ({sequence_length}, {feature_count})")
    print(f"   LSTM Layer 1: 64 units, return_sequences=True")
    print(f"   Dropout: 0.2")
    print(f"   LSTM Layer 2: 32 units, return_sequences=False")
    print(f"   Dropout: 0.2")
    print(f"   Dense Layer: 16 units, activation='relu'")
    print(f"   Output Layer: {num_classes} units, activation='softmax'")
    
    print(f"\n📈 TRAINING STRATEGY:")
    print(f"   1. Start with recommended parameters")
    print(f"   2. Monitor validation loss for overfitting")
    print(f"   3. Use early stopping to prevent overtraining")
    print(f"   4. Consider learning rate scheduling")
    print(f"   5. Evaluate on test set only once")

def create_training_example_script(metadata: Dict[str, Any], output_dir: str = "."):
    """
    Create example training script for LSTM model.
    
    Args:
        metadata: Training metadata
        output_dir: Output directory
    """
    dataset_info = metadata.get('dataset_info', {})
    recommended_params = metadata.get('recommended_training_params', {})
    
    script_content = f'''#!/usr/bin/env python3
"""
Example LSTM Training Script for Gold Trading Strategy
Generated automatically from data export pipeline
"""

import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
import matplotlib.pyplot as plt

# Load training data
print("Loading training data...")
train_data = np.load("lstm_train_final.npz")
val_data = np.load("lstm_validation_final.npz")
test_data = np.load("lstm_test_final.npz")

X_train, y_train = train_data['X'], train_data['y']
X_val, y_val = val_data['X'], val_data['y']
X_test, y_test = test_data['X'], test_data['y']

print(f"Training set: {{X_train.shape}}")
print(f"Validation set: {{X_val.shape}}")
print(f"Test set: {{X_test.shape}}")

# Build LSTM model
model = Sequential([
    LSTM(64, return_sequences=True, input_shape=({dataset_info.get('sequence_length', 48)}, {dataset_info.get('feature_count', 5)})),
    Dropout(0.2),
    LSTM(32, return_sequences=False),
    Dropout(0.2),
    Dense(16, activation='relu'),
    Dense({dataset_info.get('num_classes', 3)}, activation='softmax')
])

# Compile model
model.compile(
    optimizer=Adam(learning_rate={recommended_params.get('learning_rate', 0.001)}),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

print("Model architecture:")
model.summary()

# Define callbacks
callbacks = [
    EarlyStopping(
        monitor='val_loss',
        patience={recommended_params.get('early_stopping_patience', 10)},
        restore_best_weights=True
    ),
    ModelCheckpoint(
        'best_lstm_model.h5',
        monitor='val_loss',
        save_best_only=True
    )
]

# Train model
print("Starting training...")
history = model.fit(
    X_train, y_train,
    batch_size={recommended_params.get('batch_size', 32)},
    epochs={recommended_params.get('epochs', 100)},
    validation_data=(X_val, y_val),
    callbacks=callbacks,
    verbose=1
)

# Evaluate on test set
print("Evaluating on test set...")
test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)
print(f"Test Loss: {{test_loss:.4f}}")
print(f"Test Accuracy: {{test_accuracy:.4f}}")

# Plot training history
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(history.history['accuracy'], label='Training Accuracy')
plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
plt.title('Model Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.legend()

plt.tight_layout()
plt.savefig('training_history.png')
plt.show()

print("Training completed! Model saved as 'best_lstm_model.h5'")
'''
    
    script_path = Path(output_dir) / "example_lstm_training.py"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    print(f"\n📝 Example training script created: {script_path.name}")

def main():
    """
    Main execution function.
    """
    print("Final Data Export for LSTM Gold Trading Strategy")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease resolve issues above before continuing.")
        return False
    
    try:
        # Load configuration
        config = get_config()
        formatter_config = {
            'splits': {
                'train_ratio': 0.7,
                'validation_ratio': 0.15,
                'test_ratio': 0.15,
                'temporal_ordering': True
            },
            'export_formats': {
                'npz': True,
                'hdf5': True,
                'compression': True
            },
            'validation': {
                'check_shapes': True,
                'check_data_types': True,
                'check_temporal_order': True,
                'check_label_distribution': True
            },
            'memory_optimization': {
                'use_float32': True,
                'batch_processing': True,
                'clear_intermediate': True
            }
        }
        
        logger.info("Configuration loaded")
        
        # Load complete dataset
        print("\n📊 Loading complete dataset...")
        sequences, timestamps, labels_string, labels_onehot = load_complete_dataset("XAU_dataset_with_labels.npz")
        
        # Initialize formatter
        print("🔧 Initializing LSTM data formatter...")
        formatter = LSTMDataFormatter(formatter_config)
        
        # Create output directory
        output_dir = "lstm_training_data"
        Path(output_dir).mkdir(exist_ok=True)
        
        # Run complete formatting pipeline
        print("🔄 Running complete data formatting pipeline...")
        print("   - Input validation")
        print("   - Temporal split creation")
        print("   - Data type optimization")
        print("   - Split validation")
        print("   - Multi-format export")
        print("   - Metadata generation")
        
        formatting_results = formatter.format_for_lstm_training(
            sequences, labels_onehot, timestamps, output_dir
        )
        
        # Print results
        print_formatting_summary(formatting_results)
        
        # Print training recommendations
        metadata = formatting_results['metadata']
        print_training_recommendations(metadata)
        
        # Create example training script
        create_training_example_script(metadata, output_dir)
        
        # Final summary
        print(f"\n🎉 Final data export completed successfully!")
        print(f"📁 Output directory: {output_dir}/")
        print(f"📊 Total samples exported: {metadata['dataset_info']['total_samples']:,}")
        print(f"💾 Total data size: {metadata['dataset_info']['total_memory_mb']:.1f} MB")
        
        print(f"\n📄 Files created:")
        for file_type, filepath in formatting_results['exported_files'].items():
            print(f"   - {Path(filepath).name}")
        
        print(f"\nNext steps:")
        print("1. Review the training recommendations above")
        print("2. Use the example training script as a starting point")
        print("3. Train your LSTM model with the exported data")
        print("4. Evaluate model performance and iterate")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in final data export: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
