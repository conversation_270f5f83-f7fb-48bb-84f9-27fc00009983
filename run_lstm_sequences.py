#!/usr/bin/env python3
"""
LSTM Sequence Generation Runner for Gold Trading Strategy
========================================================

This script generates LSTM sequences from processed features using a sliding window
approach. It creates sequences of 48 timesteps with 5 features each, handling
market gaps and maintaining temporal ordering.

Usage:
    python run_lstm_sequences.py

Requirements:
    - XAU_processed.csv (output from data preprocessing)
    - All required packages installed in virtual environment

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
import json
import logging

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from lstm_sequence_generator import LSTMSequenceGenerator
from config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lstm_sequences.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """
    Check if all prerequisites are met.
    
    Returns:
        bool: True if all prerequisites are met
    """
    print("Checking prerequisites...")
    
    # Check if processed data exists
    processed_file = "XAU_processed.csv"
    if not os.path.exists(processed_file):
        print(f"❌ Error: {processed_file} not found")
        print("   Please run the data preprocessing first")
        return False
    else:
        print(f"✅ Found {processed_file}")
    
    # Check required packages
    try:
        import pandas as pd
        import numpy as np
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Error: Missing package - {e}")
        return False
    
    return True

def load_processed_data(filepath: str) -> pd.DataFrame:
    """
    Load processed data with features.
    
    Args:
        filepath: Path to CSV file
        
    Returns:
        DataFrame with processed features
    """
    logger.info(f"Loading processed data from {filepath}")
    
    df = pd.read_csv(filepath)
    
    # Convert datetime column
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    
    logger.info(f"Loaded {len(df)} records")
    logger.info(f"Date range: {df.index.min()} to {df.index.max()}")
    
    return df

def print_generation_summary(report: dict):
    """
    Print formatted sequence generation summary.
    
    Args:
        report: Generation report dictionary
    """
    print("\n" + "="*80)
    print("LSTM SEQUENCE GENERATION SUMMARY")
    print("="*80)
    
    # Input data info
    input_data = report.get('input_data', {})
    print(f"📊 Input Data: {input_data.get('total_records', 0):,} records")
    
    date_range = input_data.get('date_range', {})
    if date_range.get('start'):
        print(f"📅 Date Range: {date_range['start']} to {date_range['end']}")
    
    features_used = input_data.get('features_used', [])
    print(f"🔢 Features Used: {len(features_used)} ({', '.join(features_used)})")
    
    # Generation statistics
    gen_stats = report.get('generation_stats', {})
    print(f"\n📈 GENERATION STATISTICS:")
    print(f"   Potential Sequences: {gen_stats.get('total_potential', 0):,}")
    print(f"   Valid Generated: {gen_stats.get('valid_generated', 0):,}")
    print(f"   Rejected: {gen_stats.get('rejected', 0):,}")
    
    if gen_stats.get('valid_generated', 0) > 0:
        success_rate = gen_stats['valid_generated'] / gen_stats.get('total_potential', 1)
        print(f"   Success Rate: {success_rate:.1%}")
    
    # Rejection reasons
    rejection_reasons = gen_stats.get('rejection_reasons', {})
    if rejection_reasons:
        print(f"\n⚠️  REJECTION REASONS:")
        for reason, count in rejection_reasons.items():
            print(f"   {reason.replace('_', ' ').title()}: {count:,}")
    
    # Final shape and memory usage
    final_shape = gen_stats.get('final_shape', (0, 0, 0))
    memory_mb = gen_stats.get('memory_usage_mb', 0)
    print(f"\n💾 OUTPUT:")
    print(f"   Final Shape: {final_shape}")
    print(f"   Memory Usage: {memory_mb:.1f} MB")
    
    # Validation results
    validation = report.get('validation_results', {})
    if validation:
        print(f"\n🔍 VALIDATION: {validation.get('status', 'unknown').upper()}")
        
        issues = validation.get('issues', [])
        if issues:
            print(f"   Issues Found:")
            for issue in issues:
                print(f"     - {issue}")
        else:
            print("   ✅ No issues found")

def print_sequence_summary(summary: dict):
    """
    Print detailed sequence summary.
    
    Args:
        summary: Sequence summary dictionary
    """
    print("\n" + "="*80)
    print("SEQUENCE ANALYSIS")
    print("="*80)
    
    basic_info = summary.get('basic_info', {})
    print(f"📊 Basic Information:")
    print(f"   Total Sequences: {basic_info.get('total_sequences', 0):,}")
    print(f"   Sequence Length: {basic_info.get('sequence_length', 0)} timesteps")
    print(f"   Features per Timestep: {basic_info.get('features_per_timestep', 0)}")
    print(f"   Total Data Points: {basic_info.get('total_data_points', 0):,}")
    print(f"   Memory Usage: {basic_info.get('memory_usage_mb', 0):.1f} MB")
    
    temporal_info = summary.get('temporal_info', {})
    if temporal_info:
        print(f"\n📅 Temporal Information:")
        date_range = temporal_info.get('date_range', {})
        if date_range.get('start'):
            print(f"   Date Range: {date_range['start']} to {date_range['end']}")
        print(f"   Span: {temporal_info.get('span_days', 0)} days")
    
    data_quality = summary.get('data_quality', {})
    if data_quality:
        print(f"\n🔍 Data Quality:")
        print(f"   Finite Values: {data_quality.get('finite_ratio', 0):.1%}")
        print(f"   NaN Count: {data_quality.get('nan_count', 0)}")
        print(f"   Infinite Count: {data_quality.get('inf_count', 0)}")
        
        value_range = data_quality.get('value_range', {})
        if value_range:
            print(f"   Value Range: [{value_range.get('min', 0):.4f}, {value_range.get('max', 0):.4f}]")
            print(f"   Mean ± Std: {value_range.get('mean', 0):.4f} ± {value_range.get('std', 0):.4f}")

def save_sequences_and_splits(generator, sequences, timestamps, feature_columns):
    """
    Save sequences and create train/validation/test splits.
    
    Args:
        generator: LSTMSequenceGenerator instance
        sequences: Generated sequences array
        timestamps: Corresponding timestamps
        feature_columns: List of feature column names
    """
    print("\n💾 Saving sequences and creating splits...")
    
    # Save full sequences
    generator.save_sequences(sequences, timestamps, "XAU_sequences.npz", compression=True)
    
    # Create temporal splits
    config = get_config('lstm')
    train_ratio = config.get('train_ratio', 0.7)
    val_ratio = config.get('validation_ratio', 0.15)
    test_ratio = config.get('test_ratio', 0.15)
    
    sequence_splits, timestamp_splits = generator.split_sequences_temporal(
        sequences, timestamps, train_ratio, val_ratio, test_ratio
    )
    
    # Save splits
    for split_name, split_sequences in sequence_splits.items():
        split_timestamps = timestamp_splits[split_name]
        filename = f"XAU_sequences_{split_name}.npz"
        
        np.savez_compressed(filename, 
                           sequences=split_sequences,
                           timestamps=split_timestamps,
                           feature_columns=feature_columns)
        
        print(f"   {split_name.capitalize()}: {len(split_sequences):,} sequences → {filename}")
    
    return sequence_splits, timestamp_splits

def main():
    """
    Main execution function.
    """
    print("LSTM Sequence Generation for Gold Trading Strategy")
    print("=" * 65)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease resolve issues above before continuing.")
        return False
    
    try:
        # Load configuration
        config = get_config('lstm')
        sequence_config = {
            'sequence_length': config.get('lookback_window', 48),
            'features_per_timestep': config.get('features_per_timestep', 5),
            'min_gap_minutes': 10,
            'max_gap_hours': 72,
            'overlap_ratio': 0.0,
            'validation_checks': True,
            'memory_efficient': True
        }
        
        logger.info("Configuration loaded")
        
        # Load processed data
        df = load_processed_data("XAU_processed.csv")
        
        # Define feature columns
        feature_columns = [
            'macd_histogram_slope_norm',
            'stochastic_d_norm',
            'dpo_norm',
            'bias_ratio_norm',
            'log_returns_norm'
        ]
        
        # Verify feature columns exist
        missing_features = [col for col in feature_columns if col not in df.columns]
        if missing_features:
            logger.error(f"Missing feature columns: {missing_features}")
            return False
        
        # Initialize sequence generator
        print("\n🔧 Initializing LSTM sequence generator...")
        generator = LSTMSequenceGenerator(sequence_config)
        
        # Generate sequences
        print("🔄 Generating LSTM sequences...")
        print(f"   Sequence Length: {sequence_config['sequence_length']} timesteps")
        print(f"   Features per Timestep: {len(feature_columns)}")
        print("   Processing with gap detection and validation...")
        
        sequences, timestamps, generation_report = generator.generate_sequences(df, feature_columns)
        
        # Print results
        print_generation_summary(generation_report)
        
        if len(sequences) > 0:
            # Get detailed sequence summary
            sequence_summary = generator.get_sequence_summary(sequences, timestamps)
            print_sequence_summary(sequence_summary)
            
            # Save sequences and create splits
            sequence_splits, timestamp_splits = save_sequences_and_splits(
                generator, sequences, timestamps, feature_columns
            )
            
            # Save detailed report
            full_report = {
                'generation_report': generation_report,
                'sequence_summary': sequence_summary,
                'split_info': {
                    split_name: len(split_data) 
                    for split_name, split_data in sequence_splits.items()
                }
            }
            
            with open('lstm_sequences_report.json', 'w') as f:
                json.dump(full_report, f, indent=2, default=str)
            
            print(f"\n📄 Detailed report saved to: lstm_sequences_report.json")
            
            # Final summary
            print(f"\n🎉 LSTM sequence generation completed successfully!")
            print(f"📊 Total sequences: {len(sequences):,}")
            print(f"📈 Ready for LSTM training")
            print(f"💾 Files created:")
            print(f"   - XAU_sequences.npz (full dataset)")
            print(f"   - XAU_sequences_train.npz ({len(sequence_splits['train']):,} sequences)")
            print(f"   - XAU_sequences_validation.npz ({len(sequence_splits['validation']):,} sequences)")
            print(f"   - XAU_sequences_test.npz ({len(sequence_splits['test']):,} sequences)")
            
            print(f"\nNext steps:")
            print("1. Review the generation report above")
            print("2. Proceed with target label generation")
            print("3. Train LSTM model using the generated sequences")
            
        else:
            print("\n❌ No valid sequences generated!")
            print("Check the rejection reasons above and adjust parameters if needed.")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error in LSTM sequence generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
