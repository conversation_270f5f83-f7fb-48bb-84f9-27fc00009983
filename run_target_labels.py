#!/usr/bin/env python3
"""
Target Label Generation Runner for LSTM Gold Trading Strategy
============================================================

This script generates Buy/Sell/Hold target labels for LSTM sequences using
forward-looking price analysis based on risk management parameters:
- 60 pips take profit (2:1 R:R ratio)
- 30 pips stop loss
- 12-bar (1 hour) time horizon

Usage:
    python run_target_labels.py

Requirements:
    - XAU_processed.csv (processed OHLC data)
    - XAU_sequences.npz (generated LSTM sequences)
    - All required packages installed in virtual environment

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Tuple, Dict
import json
import logging

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from target_label_generator import TargetLabelGenerator
from config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('target_labels.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """
    Check if all prerequisites are met.
    
    Returns:
        bool: True if all prerequisites are met
    """
    print("Checking prerequisites...")
    
    # Check if processed data exists
    processed_file = "XAU_processed.csv"
    if not os.path.exists(processed_file):
        print(f"❌ Error: {processed_file} not found")
        print("   Please run the data preprocessing first")
        return False
    else:
        print(f"✅ Found {processed_file}")
    
    # Check if sequences exist
    sequences_file = "XAU_sequences.npz"
    if not os.path.exists(sequences_file):
        print(f"❌ Error: {sequences_file} not found")
        print("   Please run the LSTM sequence generation first")
        return False
    else:
        print(f"✅ Found {sequences_file}")
    
    # Check required packages
    try:
        import pandas as pd
        import numpy as np
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Error: Missing package - {e}")
        return False
    
    return True

def load_processed_data(filepath: str) -> pd.DataFrame:
    """
    Load processed OHLC data.
    
    Args:
        filepath: Path to CSV file
        
    Returns:
        DataFrame with OHLC data
    """
    logger.info(f"Loading processed data from {filepath}")
    
    df = pd.read_csv(filepath)
    
    # Convert datetime column
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    
    logger.info(f"Loaded {len(df)} OHLC records")
    logger.info(f"Date range: {df.index.min()} to {df.index.max()}")
    
    return df

def load_sequences(filepath: str) -> Tuple[np.ndarray, np.ndarray, Dict]:
    """
    Load LSTM sequences and timestamps.
    
    Args:
        filepath: Path to sequences file
        
    Returns:
        Tuple of (sequences, timestamps, metadata)
    """
    logger.info(f"Loading sequences from {filepath}")
    
    data = np.load(filepath, allow_pickle=True)
    
    sequences = data['sequences']
    timestamps = data['timestamps']
    
    metadata = {
        'config': data['config'].item() if 'config' in data else {},
        'stats': data['stats'].item() if 'stats' in data else {}
    }
    
    logger.info(f"Loaded {len(sequences)} sequences with shape {sequences.shape}")
    logger.info(f"Timestamp range: {timestamps.min()} to {timestamps.max()}")
    
    return sequences, timestamps, metadata

def print_label_generation_summary(report: dict):
    """
    Print formatted label generation summary.
    
    Args:
        report: Label generation report dictionary
    """
    print("\n" + "="*80)
    print("TARGET LABEL GENERATION SUMMARY")
    print("="*80)
    
    # Basic statistics
    total_sequences = report.get('total_sequences', 0)
    print(f"📊 Total Sequences: {total_sequences:,}")
    
    # Label distribution
    distribution = report.get('label_distribution', {})
    percentages = report.get('label_percentages', {})
    
    print(f"\n📈 LABEL DISTRIBUTION:")
    print(f"   Buy:  {distribution.get('buy', 0):,} ({percentages.get('buy', 0):.1f}%)")
    print(f"   Sell: {distribution.get('sell', 0):,} ({percentages.get('sell', 0):.1f}%)")
    print(f"   Hold: {distribution.get('hold', 0):,} ({percentages.get('hold', 0):.1f}%)")
    
    # Configuration used
    config = report.get('configuration', {})
    print(f"\n⚙️  CONFIGURATION:")
    print(f"   Take Profit: {config.get('take_profit_pips', 0)} pips")
    print(f"   Stop Loss: {config.get('stop_loss_pips', 0)} pips")
    print(f"   Time Horizon: {config.get('time_horizon_bars', 0)} bars")
    print(f"   Risk:Reward Ratio: {config.get('risk_reward_ratio', 0):.1f}:1")
    
    # Balance analysis
    buy_pct = percentages.get('buy', 0)
    sell_pct = percentages.get('sell', 0)
    hold_pct = percentages.get('hold', 0)
    
    print(f"\n🎯 BALANCE ANALYSIS:")
    if hold_pct > 70:
        print("   ⚠️  High hold percentage - consider adjusting thresholds")
    elif buy_pct + sell_pct < 20:
        print("   ⚠️  Low trading signal percentage - market may be ranging")
    else:
        print("   ✅ Reasonable balance of trading signals")
    
    if abs(buy_pct - sell_pct) > 20:
        bias = "bullish" if buy_pct > sell_pct else "bearish"
        print(f"   📊 Market bias detected: {bias} ({abs(buy_pct - sell_pct):.1f}% difference)")
    else:
        print("   ⚖️  Balanced buy/sell signals")

def save_labels_and_splits(sequences: np.ndarray, timestamps: np.ndarray, 
                          labels_array: np.ndarray, onehot_labels: np.ndarray):
    """
    Save labels and create train/validation/test splits with labels.
    
    Args:
        sequences: LSTM sequences
        timestamps: Sequence timestamps
        labels_array: String labels
        onehot_labels: One-hot encoded labels
    """
    print("\n💾 Saving labels and updating splits...")
    
    # Save full dataset with labels
    np.savez_compressed("XAU_dataset_with_labels.npz",
                       sequences=sequences,
                       timestamps=timestamps,
                       labels_string=labels_array,
                       labels_onehot=onehot_labels)
    
    print(f"   Full dataset: {len(sequences):,} samples → XAU_dataset_with_labels.npz")
    
    # Load existing splits and add labels
    split_files = [
        ("XAU_sequences_train.npz", "XAU_train_with_labels.npz"),
        ("XAU_sequences_validation.npz", "XAU_validation_with_labels.npz"),
        ("XAU_sequences_test.npz", "XAU_test_with_labels.npz")
    ]
    
    # Calculate split indices (same as used in sequence generation)
    total_sequences = len(sequences)
    train_end = int(total_sequences * 0.7)
    val_end = int(total_sequences * 0.85)
    
    splits = {
        'train': (0, train_end),
        'validation': (train_end, val_end),
        'test': (val_end, total_sequences)
    }
    
    for split_name, (start_idx, end_idx) in splits.items():
        # Extract split data
        split_sequences = sequences[start_idx:end_idx]
        split_timestamps = timestamps[start_idx:end_idx]
        split_labels_string = labels_array[start_idx:end_idx]
        split_labels_onehot = onehot_labels[start_idx:end_idx]
        
        # Save split with labels
        output_file = f"XAU_{split_name}_with_labels.npz"
        np.savez_compressed(output_file,
                           sequences=split_sequences,
                           timestamps=split_timestamps,
                           labels_string=split_labels_string,
                           labels_onehot=split_labels_onehot)
        
        print(f"   {split_name.capitalize()}: {len(split_sequences):,} samples → {output_file}")
        
        # Print split label distribution
        split_distribution = {
            'buy': int(np.sum(split_labels_string == 'buy')),
            'sell': int(np.sum(split_labels_string == 'sell')),
            'hold': int(np.sum(split_labels_string == 'hold'))
        }
        print(f"     Distribution - Buy: {split_distribution['buy']}, "
              f"Sell: {split_distribution['sell']}, Hold: {split_distribution['hold']}")

def main():
    """
    Main execution function.
    """
    print("Target Label Generation for LSTM Gold Trading Strategy")
    print("=" * 65)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease resolve issues above before continuing.")
        return False
    
    try:
        # Load configuration
        config = get_config('risk_management')
        label_config = {
            'take_profit_pips': config.get('take_profit_pips', 60),
            'stop_loss_pips': config.get('stop_loss_pips', 30),
            'time_horizon_bars': config.get('time_horizon_bars', 12),
            'risk_reward_ratio': config.get('risk_reward_ratio', 2.0),
            'pip_value': 0.01,  # For XAUUSD
            'min_price_movement': 0.10,
            'hold_threshold': 0.6,  # 60% confidence threshold
            'label_encoding': {
                'hold': 0,
                'buy': 1,
                'sell': 2
            }
        }
        
        logger.info("Configuration loaded")
        
        # Load processed data
        df = load_processed_data("XAU_processed.csv")
        
        # Load sequences
        sequences, timestamps, seq_metadata = load_sequences("XAU_sequences.npz")
        
        # Initialize label generator
        print("\n🔧 Initializing target label generator...")
        label_generator = TargetLabelGenerator(label_config)
        
        # Generate labels
        print("🔄 Generating target labels...")
        print(f"   Analyzing {len(sequences):,} sequences")
        print(f"   Using {label_config['take_profit_pips']} pips TP, {label_config['stop_loss_pips']} pips SL")
        print(f"   Time horizon: {label_config['time_horizon_bars']} bars (1 hour)")
        print("   Processing forward-looking price analysis...")
        
        labels_array, generation_report = label_generator.generate_labels_for_sequences(df, timestamps)
        
        # Convert to one-hot encoding
        print("\n🔢 Converting to one-hot encoding...")
        onehot_labels = label_generator.encode_labels_onehot(labels_array)
        
        # Print results
        print_label_generation_summary(generation_report)
        
        # Save results
        save_labels_and_splits(sequences, timestamps, labels_array, onehot_labels)
        
        # Save detailed report
        full_report = {
            'generation_report': generation_report,
            'sequence_metadata': seq_metadata,
            'label_config': label_config,
            'onehot_shape': onehot_labels.shape
        }
        
        with open('target_labels_report.json', 'w') as f:
            json.dump(full_report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: target_labels_report.json")
        
        # Final summary
        print(f"\n🎉 Target label generation completed successfully!")
        print(f"📊 Generated labels for {len(sequences):,} sequences")
        print(f"🔢 One-hot encoded shape: {onehot_labels.shape}")
        print(f"💾 Files created:")
        print(f"   - XAU_dataset_with_labels.npz (full dataset)")
        print(f"   - XAU_train_with_labels.npz (training set)")
        print(f"   - XAU_validation_with_labels.npz (validation set)")
        print(f"   - XAU_test_with_labels.npz (test set)")
        
        print(f"\nNext steps:")
        print("1. Review the label distribution above")
        print("2. Proceed with LSTM model training")
        print("3. Evaluate model performance on validation set")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in target label generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
