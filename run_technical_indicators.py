#!/usr/bin/env python3
"""
Technical Indicators Calculator Runner
=====================================

This script calculates all required technical indicators for the LSTM gold trading strategy.
It processes the cleaned XAUUSD data and generates normalized features ready for LSTM training.

Usage:
    python run_technical_indicators.py

Requirements:
    - XAU_5m_data.csv (processed data from data preparation pipeline)
    - All required packages installed in virtual environment

Author: AI Assistant
Date: 2025-01-24
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
import json
import logging

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from technical_indicators import TechnicalIndicators
from config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('technical_indicators.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_prerequisites():
    """
    Check if all prerequisites are met.
    
    Returns:
        bool: True if all prerequisites are met
    """
    print("Checking prerequisites...")
    
    # Check if processed data exists
    data_file = "XAU_5m_data.csv"
    if not os.path.exists(data_file):
        print(f"❌ Error: {data_file} not found")
        print("   Please run the data preparation pipeline first")
        return False
    else:
        print(f"✅ Found {data_file}")
    
    # Check required packages
    try:
        import pandas as pd
        import numpy as np
        import sklearn
        import ta
        print("✅ All required packages available")
    except ImportError as e:
        print(f"❌ Error: Missing package - {e}")
        return False
    
    return True

def load_data(filepath: str) -> pd.DataFrame:
    """
    Load processed XAUUSD data.
    
    Args:
        filepath: Path to CSV file
        
    Returns:
        DataFrame with OHLC data
    """
    logger.info(f"Loading data from {filepath}")
    
    df = pd.read_csv(filepath)
    
    # Convert datetime column
    df['datetime'] = pd.to_datetime(df['datetime'])
    df.set_index('datetime', inplace=True)
    
    logger.info(f"Loaded {len(df)} records")
    logger.info(f"Date range: {df.index.min()} to {df.index.max()}")
    
    return df

def print_indicator_summary(summary: dict):
    """
    Print formatted summary of indicators.
    
    Args:
        summary: Summary statistics dictionary
    """
    print("\n" + "="*80)
    print("TECHNICAL INDICATORS SUMMARY")
    print("="*80)
    
    dataset_info = summary.get('dataset_info', {})
    print(f"📊 Dataset: {dataset_info.get('total_records', 0):,} records")
    
    date_range = dataset_info.get('date_range', {})
    if date_range.get('start'):
        print(f"📅 Date Range: {date_range['start']} to {date_range['end']}")
    
    print("\n🔢 INDICATORS STATISTICS:")
    print("-" * 80)
    
    indicators = summary.get('indicators_summary', {})
    
    for indicator, stats in indicators.items():
        print(f"\n{indicator.upper().replace('_', ' ')}:")
        print(f"  Valid Values: {stats['count']:,} ({100*stats['count']/(stats['count']+stats['missing']):.1f}%)")
        print(f"  Range: [{stats['min']:.4f}, {stats['max']:.4f}]")
        print(f"  Mean ± Std: {stats['mean']:.4f} ± {stats['std']:.4f}")
        print(f"  Quartiles: Q25={stats['q25']:.4f}, Q50={stats['q50']:.4f}, Q75={stats['q75']:.4f}")

def print_validation_report(validation: dict):
    """
    Print formatted validation report.
    
    Args:
        validation: Validation results dictionary
    """
    print("\n" + "="*80)
    print("INDICATORS VALIDATION REPORT")
    print("="*80)
    
    print(f"📋 Total Records: {validation['total_records']:,}")
    print(f"🎯 Overall Quality: {validation['overall_quality'].upper()}")
    
    issues = validation.get('issues', [])
    if issues:
        print(f"\n⚠️  ISSUES FOUND ({len(issues)}):")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    else:
        print("\n✅ NO ISSUES FOUND")
    
    print("\n📈 INDICATOR DETAILS:")
    print("-" * 80)
    
    for indicator, stats in validation.get('indicators', {}).items():
        missing_pct = stats['missing_percentage']
        status = "✅" if missing_pct < 5 else "⚠️" if missing_pct < 10 else "❌"
        
        print(f"{status} {indicator}:")
        print(f"    Valid: {stats['valid_values']:,} ({100-missing_pct:.1f}%)")
        print(f"    Range: [{stats['min_value']:.4f}, {stats['max_value']:.4f}]")

def save_results(df: pd.DataFrame, output_path: str = "XAU_features.csv"):
    """
    Save results to CSV file.
    
    Args:
        df: DataFrame with calculated indicators
        output_path: Output file path
    """
    logger.info(f"Saving results to {output_path}")
    
    # Reset index to save datetime as column
    df_save = df.reset_index()
    
    # Convert datetime to string for CSV compatibility
    df_save['datetime'] = df_save['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Save to CSV
    df_save.to_csv(output_path, index=False)
    
    logger.info(f"Results saved: {len(df_save)} records to {output_path}")

def main():
    """
    Main execution function.
    """
    print("Technical Indicators Calculator")
    print("=" * 50)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\nPlease resolve issues above before continuing.")
        return False
    
    try:
        # Load configuration
        config = get_config('indicators')
        logger.info("Configuration loaded")
        
        # Load data
        df = load_data("XAU_5m_data.csv")
        
        # Initialize technical indicators calculator
        print("\n🔧 Initializing technical indicators calculator...")
        calculator = TechnicalIndicators(config)
        
        # Calculate all indicators
        print("📊 Calculating technical indicators...")
        print("   - MACD Histogram Slope (linear regression)")
        print("   - 14-period Stochastic %D")
        print("   - 10-period Detrended Price Oscillator (DPO)")
        print("   - 20-period Bias Ratio")
        print("   - Log Returns")
        
        df_with_indicators = calculator.calculate_all_indicators(df, normalize=True)
        
        # Validate indicators
        print("\n🔍 Validating indicators...")
        validation_report = calculator.validate_indicators(df_with_indicators)
        
        # Get summary statistics
        summary_stats = calculator.get_summary_statistics(df_with_indicators)
        
        # Print results
        print_indicator_summary(summary_stats)
        print_validation_report(validation_report)
        
        # Save results
        print("\n💾 Saving results...")
        save_results(df_with_indicators, "XAU_features.csv")
        
        # Save scalers for future use
        calculator.save_scalers("indicator_scalers.pkl")
        
        # Save detailed report
        report = {
            'summary_statistics': summary_stats,
            'validation_report': validation_report,
            'feature_columns': calculator.get_feature_columns(normalized=True),
            'configuration': config
        }
        
        with open('technical_indicators_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to: technical_indicators_report.json")
        print(f"🔧 Scalers saved to: indicator_scalers.pkl")
        
        # Final summary
        feature_cols = calculator.get_feature_columns(normalized=True)
        valid_features = df_with_indicators[feature_cols].dropna()
        
        print(f"\n🎉 Technical indicators calculation completed!")
        print(f"📈 Features ready for LSTM: {len(feature_cols)} indicators")
        print(f"📊 Valid feature records: {len(valid_features):,}")
        print(f"💾 Output file: XAU_features.csv")
        
        print(f"\nNext steps:")
        print("1. Review the validation report above")
        print("2. Check XAU_features.csv for calculated indicators")
        print("3. Proceed with LSTM sequence generation")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in technical indicators calculation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
