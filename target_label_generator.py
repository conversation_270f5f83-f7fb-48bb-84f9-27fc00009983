"""
Target Label Generation Module for LSTM Gold Trading Strategy
============================================================

This module implements forward-looking analysis to generate Buy/Sell/Hold labels
based on the specified risk management parameters:
- 60 pips take profit (2:1 R:R ratio)
- 30 pips stop loss
- 12-bar (1 hour) time horizon
- 0.25% risk per trade

Key Features:
- Forward-looking price analysis
- Risk-reward ratio calculation
- Multi-class classification (Buy/Sell/Hold)
- Proper handling of market gaps
- One-hot encoding for LSTM training

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Dict, Any, Optional
import logging
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class TargetLabelGenerator:
    """
    Generate target labels for LSTM trading strategy using forward-looking analysis.
    Implements Buy/Sell/Hold classification based on risk management parameters.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize target label generator.
        
        Args:
            config: Configuration dictionary with labeling parameters
        """
        # Default configuration based on strategy requirements
        self.config = config or {
            'take_profit_pips': 60,  # 60 pips take profit
            'stop_loss_pips': 30,   # 30 pips stop loss
            'time_horizon_bars': 12,  # 12 bars = 1 hour (5-minute bars)
            'risk_reward_ratio': 2.0,  # 2:1 R:R ratio
            'pip_value': 0.0001,  # For XAUUSD, 1 pip = 0.01 (but we use 0.0001 for precision)
            'min_price_movement': 0.10,  # Minimum price movement to consider (10 cents)
            'hold_threshold': 0.5,  # Threshold for hold classification
            'label_encoding': {
                'hold': 0,
                'buy': 1,
                'sell': 2
            }
        }
        
        # Label statistics
        self.label_stats = {
            'total_labels': 0,
            'buy_count': 0,
            'sell_count': 0,
            'hold_count': 0,
            'label_distribution': {},
            'avg_profit_potential': {},
            'avg_loss_potential': {}
        }
        
        logger.info("Target label generator initialized")
    
    def calculate_pip_movement(self, price_start: float, price_end: float) -> float:
        """
        Calculate pip movement between two prices.
        
        Args:
            price_start: Starting price
            price_end: Ending price
            
        Returns:
            Pip movement (positive for upward movement)
        """
        # For XAUUSD, 1 pip = 0.01 (but we calculate in smaller units for precision)
        pip_movement = (price_end - price_start) / 0.01
        return pip_movement
    
    def analyze_forward_price_action(self, df: pd.DataFrame, current_idx: int) -> Dict[str, Any]:
        """
        Analyze forward price action from current position.
        
        Args:
            df: DataFrame with OHLC data
            current_idx: Current position index
            
        Returns:
            Dictionary with forward analysis results
        """
        time_horizon = self.config['time_horizon_bars']
        take_profit_pips = self.config['take_profit_pips']
        stop_loss_pips = self.config['stop_loss_pips']
        
        # Get current price (close price at current bar)
        current_price = df.iloc[current_idx]['close']
        
        # Define forward window
        end_idx = min(current_idx + time_horizon, len(df) - 1)
        forward_window = df.iloc[current_idx + 1:end_idx + 1]
        
        if len(forward_window) == 0:
            return {
                'label': 'hold',
                'reason': 'insufficient_forward_data',
                'max_profit_pips': 0,
                'max_loss_pips': 0,
                'hit_take_profit': False,
                'hit_stop_loss': False
            }
        
        # Calculate price levels
        buy_take_profit = current_price + (take_profit_pips * 0.01)
        buy_stop_loss = current_price - (stop_loss_pips * 0.01)
        sell_take_profit = current_price - (take_profit_pips * 0.01)
        sell_stop_loss = current_price + (stop_loss_pips * 0.01)
        
        # Analyze buy scenario
        buy_analysis = self._analyze_trade_scenario(
            forward_window, current_price, buy_take_profit, buy_stop_loss, 'buy'
        )
        
        # Analyze sell scenario
        sell_analysis = self._analyze_trade_scenario(
            forward_window, current_price, sell_take_profit, sell_stop_loss, 'sell'
        )
        
        # Determine best label based on analysis
        label_result = self._determine_optimal_label(buy_analysis, sell_analysis)
        
        return label_result
    
    def _analyze_trade_scenario(self, forward_window: pd.DataFrame, entry_price: float,
                               take_profit: float, stop_loss: float, direction: str) -> Dict[str, Any]:
        """
        Analyze a specific trade scenario (buy or sell).
        
        Args:
            forward_window: Forward price data
            entry_price: Entry price
            take_profit: Take profit level
            stop_loss: Stop loss level
            direction: Trade direction ('buy' or 'sell')
            
        Returns:
            Dictionary with trade scenario analysis
        """
        analysis = {
            'direction': direction,
            'entry_price': entry_price,
            'take_profit': take_profit,
            'stop_loss': stop_loss,
            'hit_take_profit': False,
            'hit_stop_loss': False,
            'bars_to_tp': None,
            'bars_to_sl': None,
            'max_favorable_pips': 0,
            'max_adverse_pips': 0,
            'final_pips': 0,
            'profit_probability': 0
        }
        
        for i, (_, row) in enumerate(forward_window.iterrows()):
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            
            if direction == 'buy':
                # Check if take profit hit
                if high_price >= take_profit and not analysis['hit_take_profit']:
                    analysis['hit_take_profit'] = True
                    analysis['bars_to_tp'] = i + 1
                
                # Check if stop loss hit
                if low_price <= stop_loss and not analysis['hit_stop_loss']:
                    analysis['hit_stop_loss'] = True
                    analysis['bars_to_sl'] = i + 1
                
                # Calculate pip movements
                favorable_pips = self.calculate_pip_movement(entry_price, high_price)
                adverse_pips = self.calculate_pip_movement(entry_price, low_price)
                
                analysis['max_favorable_pips'] = max(analysis['max_favorable_pips'], favorable_pips)
                analysis['max_adverse_pips'] = min(analysis['max_adverse_pips'], adverse_pips)
                
            else:  # sell
                # Check if take profit hit
                if low_price <= take_profit and not analysis['hit_take_profit']:
                    analysis['hit_take_profit'] = True
                    analysis['bars_to_tp'] = i + 1
                
                # Check if stop loss hit
                if high_price >= stop_loss and not analysis['hit_stop_loss']:
                    analysis['hit_stop_loss'] = True
                    analysis['bars_to_sl'] = i + 1
                
                # Calculate pip movements (negative for sell profits)
                favorable_pips = self.calculate_pip_movement(entry_price, low_price)
                adverse_pips = self.calculate_pip_movement(entry_price, high_price)
                
                analysis['max_favorable_pips'] = min(analysis['max_favorable_pips'], favorable_pips)
                analysis['max_adverse_pips'] = max(analysis['max_adverse_pips'], adverse_pips)
        
        # Calculate final result
        if analysis['hit_take_profit'] and analysis['hit_stop_loss']:
            # Both hit - determine which came first
            if analysis['bars_to_tp'] <= analysis['bars_to_sl']:
                analysis['final_pips'] = self.config['take_profit_pips'] if direction == 'buy' else -self.config['take_profit_pips']
                analysis['profit_probability'] = 1.0
            else:
                analysis['final_pips'] = -self.config['stop_loss_pips'] if direction == 'buy' else self.config['stop_loss_pips']
                analysis['profit_probability'] = 0.0
        elif analysis['hit_take_profit']:
            analysis['final_pips'] = self.config['take_profit_pips'] if direction == 'buy' else -self.config['take_profit_pips']
            analysis['profit_probability'] = 1.0
        elif analysis['hit_stop_loss']:
            analysis['final_pips'] = -self.config['stop_loss_pips'] if direction == 'buy' else self.config['stop_loss_pips']
            analysis['profit_probability'] = 0.0
        else:
            # Neither hit - use final price
            final_price = forward_window.iloc[-1]['close']
            if direction == 'buy':
                analysis['final_pips'] = self.calculate_pip_movement(entry_price, final_price)
            else:
                analysis['final_pips'] = -self.calculate_pip_movement(entry_price, final_price)
            
            # Estimate probability based on how close we got to targets
            if direction == 'buy':
                if analysis['max_favorable_pips'] > 0:
                    analysis['profit_probability'] = min(1.0, analysis['max_favorable_pips'] / self.config['take_profit_pips'])
                else:
                    analysis['profit_probability'] = 0.0
            else:
                if analysis['max_favorable_pips'] < 0:
                    analysis['profit_probability'] = min(1.0, abs(analysis['max_favorable_pips']) / self.config['take_profit_pips'])
                else:
                    analysis['profit_probability'] = 0.0
        
        return analysis
    
    def _determine_optimal_label(self, buy_analysis: Dict[str, Any], 
                                sell_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Determine optimal label based on buy and sell analysis.
        
        Args:
            buy_analysis: Buy scenario analysis
            sell_analysis: Sell scenario analysis
            
        Returns:
            Dictionary with optimal label and reasoning
        """
        hold_threshold = self.config['hold_threshold']
        
        # Calculate expected returns
        buy_expected_return = buy_analysis['final_pips'] * buy_analysis['profit_probability']
        sell_expected_return = sell_analysis['final_pips'] * sell_analysis['profit_probability']
        
        # Determine label based on expected returns and probability thresholds
        if buy_analysis['profit_probability'] >= hold_threshold and buy_expected_return > sell_expected_return:
            if buy_expected_return > 10:  # Minimum expected profit threshold
                label = 'buy'
                confidence = buy_analysis['profit_probability']
            else:
                label = 'hold'
                confidence = 0.5
        elif sell_analysis['profit_probability'] >= hold_threshold and sell_expected_return > buy_expected_return:
            if sell_expected_return > 10:  # Minimum expected profit threshold
                label = 'sell'
                confidence = sell_analysis['profit_probability']
            else:
                label = 'hold'
                confidence = 0.5
        else:
            label = 'hold'
            confidence = 0.5
        
        return {
            'label': label,
            'confidence': confidence,
            'buy_analysis': buy_analysis,
            'sell_analysis': sell_analysis,
            'buy_expected_return': buy_expected_return,
            'sell_expected_return': sell_expected_return,
            'reason': f'best_expected_return_{label}'
        }
    
    def generate_labels_for_sequences(self, df: pd.DataFrame, sequence_timestamps: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """
        Generate labels for LSTM sequences based on their timestamps.
        
        Args:
            df: DataFrame with OHLC data
            sequence_timestamps: Array of sequence end timestamps
            
        Returns:
            Tuple of (labels_array, generation_report)
        """
        logger.info(f"Generating labels for {len(sequence_timestamps)} sequences")
        
        labels = []
        label_details = []
        
        # Convert timestamps to datetime index for faster lookup
        df_indexed = df.copy()
        if not isinstance(df_indexed.index, pd.DatetimeIndex):
            df_indexed['datetime'] = pd.to_datetime(df_indexed['datetime'])
            df_indexed.set_index('datetime', inplace=True)
        
        for i, timestamp in enumerate(sequence_timestamps):
            try:
                # Find the corresponding index in the DataFrame
                timestamp_dt = pd.to_datetime(timestamp)
                
                # Find closest timestamp in DataFrame
                closest_idx = df_indexed.index.get_indexer([timestamp_dt], method='nearest')[0]
                
                if closest_idx >= 0 and closest_idx < len(df_indexed):
                    # Generate label for this position
                    label_result = self.analyze_forward_price_action(df_indexed, closest_idx)
                    
                    labels.append(label_result['label'])
                    label_details.append(label_result)
                else:
                    # Default to hold if timestamp not found
                    labels.append('hold')
                    label_details.append({
                        'label': 'hold',
                        'reason': 'timestamp_not_found',
                        'confidence': 0.5
                    })
                
            except Exception as e:
                logger.warning(f"Error generating label for sequence {i}: {e}")
                labels.append('hold')
                label_details.append({
                    'label': 'hold',
                    'reason': 'error',
                    'confidence': 0.5
                })
            
            # Log progress
            if (i + 1) % 500 == 0:
                logger.info(f"Generated labels for {i + 1}/{len(sequence_timestamps)} sequences")
        
        # Convert to numpy array and encode
        labels_array = np.array(labels)
        
        # Update statistics
        self._update_label_statistics(labels_array, label_details)
        
        # Create generation report
        generation_report = {
            'total_sequences': len(sequence_timestamps),
            'label_distribution': {
                'buy': int(np.sum(labels_array == 'buy')),
                'sell': int(np.sum(labels_array == 'sell')),
                'hold': int(np.sum(labels_array == 'hold'))
            },
            'label_percentages': {
                'buy': float(np.sum(labels_array == 'buy') / len(labels_array) * 100),
                'sell': float(np.sum(labels_array == 'sell') / len(labels_array) * 100),
                'hold': float(np.sum(labels_array == 'hold') / len(labels_array) * 100)
            },
            'configuration': self.config,
            'statistics': self.label_stats
        }
        
        logger.info("Label generation completed")
        logger.info(f"Distribution - Buy: {generation_report['label_distribution']['buy']}, "
                   f"Sell: {generation_report['label_distribution']['sell']}, "
                   f"Hold: {generation_report['label_distribution']['hold']}")
        
        return labels_array, generation_report
    
    def _update_label_statistics(self, labels_array: np.ndarray, label_details: List[Dict]) -> None:
        """
        Update label statistics.
        
        Args:
            labels_array: Array of generated labels
            label_details: List of detailed label information
        """
        self.label_stats.update({
            'total_labels': len(labels_array),
            'buy_count': int(np.sum(labels_array == 'buy')),
            'sell_count': int(np.sum(labels_array == 'sell')),
            'hold_count': int(np.sum(labels_array == 'hold'))
        })
        
        # Calculate distribution
        total = len(labels_array)
        self.label_stats['label_distribution'] = {
            'buy': self.label_stats['buy_count'] / total,
            'sell': self.label_stats['sell_count'] / total,
            'hold': self.label_stats['hold_count'] / total
        }
    
    def encode_labels_onehot(self, labels_array: np.ndarray) -> np.ndarray:
        """
        Convert string labels to one-hot encoded format.
        
        Args:
            labels_array: Array of string labels
            
        Returns:
            One-hot encoded labels array
        """
        logger.info("Converting labels to one-hot encoding")
        
        # Create mapping
        label_to_idx = self.config['label_encoding']
        
        # Convert to indices
        label_indices = np.array([label_to_idx[label] for label in labels_array])
        
        # Create one-hot encoding
        num_classes = len(label_to_idx)
        onehot_labels = np.zeros((len(labels_array), num_classes))
        onehot_labels[np.arange(len(labels_array)), label_indices] = 1
        
        logger.info(f"One-hot encoding completed: {onehot_labels.shape}")
        return onehot_labels
