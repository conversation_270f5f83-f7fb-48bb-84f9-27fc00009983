"""
Technical Indicators Module for LSTM Gold Trading Strategy
=========================================================

This module implements the 4 required technical indicators for the LSTM-based
gold trading strategy as specified in the CUHK 2023 research:

1. MACD Histogram Slope (linear regression over 3-5 bars)
2. 14-period Stochastic %D
3. 10-period Detrended Price Oscillator (DPO)
4. 20-period Bias Ratio

All indicators are normalized to [-1,1] or [0,1] ranges as required.

Author: AI Assistant
Date: 2025-01-24
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.linear_model import LinearRegression
import ta
import logging
from typing import Dict, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)

class TechnicalIndicators:
    """
    Comprehensive technical indicators calculator for LSTM trading strategy.
    Implements all required indicators with proper normalization.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize technical indicators calculator.
        
        Args:
            config: Configuration dictionary with indicator parameters
        """
        # Default configuration
        self.config = config or {
            'macd': {
                'fast_period': 12,
                'slow_period': 26,
                'signal_period': 9,
                'slope_periods': 5
            },
            'stochastic': {
                'k_period': 14,
                'd_period': 3,
                'smooth_k': 3
            },
            'dpo': {
                'period': 10
            },
            'bias_ratio': {
                'sma_period': 20
            },
            'normalization': {
                'method': 'minmax',  # 'minmax' or 'standard'
                'feature_range': (-1, 1)
            }
        }
        
        # Initialize scalers for normalization
        self.scalers = {}
        self.is_fitted = False
        
        logger.info("Technical indicators calculator initialized")
    
    def calculate_macd_histogram_slope(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate MACD Histogram Slope using linear regression over specified periods.
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Series with MACD histogram slope values
        """
        logger.info("Calculating MACD Histogram Slope")
        
        # Calculate MACD components
        macd_config = self.config['macd']
        
        # Calculate MACD line and signal line
        exp1 = df['close'].ewm(span=macd_config['fast_period']).mean()
        exp2 = df['close'].ewm(span=macd_config['slow_period']).mean()
        macd_line = exp1 - exp2
        signal_line = macd_line.ewm(span=macd_config['signal_period']).mean()
        
        # Calculate MACD histogram
        macd_histogram = macd_line - signal_line
        
        # Calculate slope using linear regression over specified periods
        slope_periods = macd_config['slope_periods']
        slopes = []
        
        for i in range(len(macd_histogram)):
            if i < slope_periods - 1:
                slopes.append(np.nan)
            else:
                # Get the last 'slope_periods' values
                y_values = macd_histogram.iloc[i-slope_periods+1:i+1].values
                x_values = np.arange(len(y_values)).reshape(-1, 1)
                
                # Fit linear regression
                if not np.isnan(y_values).any():
                    lr = LinearRegression()
                    lr.fit(x_values, y_values)
                    slope = lr.coef_[0]
                    slopes.append(slope)
                else:
                    slopes.append(np.nan)
        
        macd_slope = pd.Series(slopes, index=df.index, name='macd_histogram_slope')
        
        logger.info(f"MACD Histogram Slope calculated: {len(macd_slope.dropna())} valid values")
        return macd_slope
    
    def calculate_stochastic_d(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate 14-period Stochastic %D oscillator.
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Series with Stochastic %D values (0-100 range)
        """
        logger.info("Calculating 14-period Stochastic %D")
        
        stoch_config = self.config['stochastic']
        
        # Calculate Stochastic %K
        lowest_low = df['low'].rolling(window=stoch_config['k_period']).min()
        highest_high = df['high'].rolling(window=stoch_config['k_period']).max()
        
        k_percent = 100 * ((df['close'] - lowest_low) / (highest_high - lowest_low))
        
        # Smooth %K if specified
        if stoch_config['smooth_k'] > 1:
            k_percent = k_percent.rolling(window=stoch_config['smooth_k']).mean()
        
        # Calculate %D (moving average of %K)
        d_percent = k_percent.rolling(window=stoch_config['d_period']).mean()
        
        d_percent.name = 'stochastic_d'
        
        logger.info(f"Stochastic %D calculated: {len(d_percent.dropna())} valid values")
        return d_percent
    
    def calculate_dpo(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate 10-period Detrended Price Oscillator (DPO).
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Series with DPO values
        """
        logger.info("Calculating 10-period Detrended Price Oscillator (DPO)")
        
        period = self.config['dpo']['period']
        
        # Calculate Simple Moving Average
        sma = df['close'].rolling(window=period).mean()
        
        # Calculate DPO: Price - SMA shifted back by (period/2 + 1) bars
        shift_periods = int(period / 2) + 1
        dpo = df['close'] - sma.shift(shift_periods)
        
        dpo.name = 'dpo'
        
        logger.info(f"DPO calculated: {len(dpo.dropna())} valid values")
        return dpo
    
    def calculate_bias_ratio(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate 20-period Bias Ratio.
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Series with Bias Ratio values (percentage)
        """
        logger.info("Calculating 20-period Bias Ratio")
        
        period = self.config['bias_ratio']['sma_period']
        
        # Calculate Simple Moving Average
        sma = df['close'].rolling(window=period).mean()
        
        # Calculate Bias Ratio: (Close - SMA20) / SMA20 * 100
        bias_ratio = ((df['close'] - sma) / sma) * 100
        
        bias_ratio.name = 'bias_ratio'
        
        logger.info(f"Bias Ratio calculated: {len(bias_ratio.dropna())} valid values")
        return bias_ratio
    
    def calculate_log_returns(self, df: pd.DataFrame) -> pd.Series:
        """
        Calculate log returns of current bar.
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            Series with log returns
        """
        logger.info("Calculating log returns")
        
        # Calculate log returns: ln(Close_t / Close_t-1)
        log_returns = np.log(df['close'] / df['close'].shift(1))
        
        log_returns.name = 'log_returns'
        
        logger.info(f"Log returns calculated: {len(log_returns.dropna())} valid values")
        return log_returns
    
    def normalize_indicator(self, series: pd.Series, method: str = None, 
                          feature_range: Tuple[float, float] = None) -> pd.Series:
        """
        Normalize indicator values to specified range.
        
        Args:
            series: Input series to normalize
            method: Normalization method ('minmax' or 'standard')
            feature_range: Target range for MinMaxScaler
            
        Returns:
            Normalized series
        """
        if method is None:
            method = self.config['normalization']['method']
        if feature_range is None:
            feature_range = tuple(self.config['normalization']['feature_range'])
        
        # Remove NaN values for fitting
        valid_data = series.dropna()
        if len(valid_data) == 0:
            logger.warning(f"No valid data for normalization of {series.name}")
            return series
        
        # Initialize scaler
        scaler_key = f"{series.name}_{method}"
        
        if method == 'minmax':
            scaler = MinMaxScaler(feature_range=feature_range)
        elif method == 'standard':
            scaler = StandardScaler()
        else:
            raise ValueError(f"Unknown normalization method: {method}")
        
        # Fit scaler on valid data
        scaler.fit(valid_data.values.reshape(-1, 1))
        self.scalers[scaler_key] = scaler
        
        # Transform all data (including NaN)
        normalized_values = series.copy()
        valid_mask = ~series.isna()
        
        if valid_mask.any():
            normalized_values.loc[valid_mask] = scaler.transform(
                series.loc[valid_mask].values.reshape(-1, 1)
            ).flatten()
        
        logger.info(f"Normalized {series.name}: range [{normalized_values.min():.3f}, {normalized_values.max():.3f}]")
        return normalized_values

    def calculate_all_indicators(self, df: pd.DataFrame, normalize: bool = True) -> pd.DataFrame:
        """
        Calculate all required technical indicators.

        Args:
            df: DataFrame with OHLC data
            normalize: Whether to normalize indicators

        Returns:
            DataFrame with all calculated indicators
        """
        logger.info("Calculating all technical indicators")

        # Validate input data
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Create result DataFrame
        result_df = df.copy()

        # Calculate each indicator
        indicators = {}

        # 1. MACD Histogram Slope
        indicators['macd_histogram_slope'] = self.calculate_macd_histogram_slope(df)

        # 2. Stochastic %D
        indicators['stochastic_d'] = self.calculate_stochastic_d(df)

        # 3. Detrended Price Oscillator (DPO)
        indicators['dpo'] = self.calculate_dpo(df)

        # 4. Bias Ratio
        indicators['bias_ratio'] = self.calculate_bias_ratio(df)

        # 5. Log Returns
        indicators['log_returns'] = self.calculate_log_returns(df)

        # Add indicators to result DataFrame
        for name, series in indicators.items():
            result_df[name] = series

        # Normalize indicators if requested
        if normalize:
            logger.info("Normalizing indicators")

            # Normalize MACD Histogram Slope to [-1, 1]
            result_df['macd_histogram_slope_norm'] = self.normalize_indicator(
                result_df['macd_histogram_slope'], 'minmax', (-1, 1)
            )

            # Normalize Stochastic %D to [0, 1] (already 0-100, just scale)
            result_df['stochastic_d_norm'] = result_df['stochastic_d'] / 100.0

            # Normalize DPO to [-1, 1]
            result_df['dpo_norm'] = self.normalize_indicator(
                result_df['dpo'], 'minmax', (-1, 1)
            )

            # Normalize Bias Ratio to [-1, 1]
            result_df['bias_ratio_norm'] = self.normalize_indicator(
                result_df['bias_ratio'], 'minmax', (-1, 1)
            )

            # Log returns are already in a reasonable range, but normalize for consistency
            result_df['log_returns_norm'] = self.normalize_indicator(
                result_df['log_returns'], 'minmax', (-1, 1)
            )

        self.is_fitted = True

        logger.info(f"All indicators calculated for {len(result_df)} records")
        return result_df

    def get_feature_columns(self, normalized: bool = True) -> list:
        """
        Get list of feature columns for LSTM input.

        Args:
            normalized: Whether to return normalized column names

        Returns:
            List of feature column names
        """
        if normalized:
            return [
                'macd_histogram_slope_norm',
                'stochastic_d_norm',
                'dpo_norm',
                'bias_ratio_norm',
                'log_returns_norm'
            ]
        else:
            return [
                'macd_histogram_slope',
                'stochastic_d',
                'dpo',
                'bias_ratio',
                'log_returns'
            ]

    def validate_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate calculated indicators for quality and completeness.

        Args:
            df: DataFrame with calculated indicators

        Returns:
            Dictionary with validation results
        """
        logger.info("Validating calculated indicators")

        feature_cols = self.get_feature_columns(normalized=True)
        validation_report = {
            'total_records': len(df),
            'indicators': {},
            'overall_quality': 'good',
            'issues': []
        }

        for col in feature_cols:
            if col not in df.columns:
                validation_report['issues'].append(f"Missing indicator: {col}")
                continue

            series = df[col]
            indicator_stats = {
                'total_values': len(series),
                'valid_values': series.notna().sum(),
                'missing_values': series.isna().sum(),
                'missing_percentage': (series.isna().sum() / len(series)) * 100,
                'min_value': series.min(),
                'max_value': series.max(),
                'mean_value': series.mean(),
                'std_value': series.std()
            }

            # Check for issues
            if indicator_stats['missing_percentage'] > 10:
                validation_report['issues'].append(
                    f"{col}: High missing data ({indicator_stats['missing_percentage']:.1f}%)"
                )

            # Check normalization ranges
            if 'norm' in col:
                if indicator_stats['min_value'] < -1.1 or indicator_stats['max_value'] > 1.1:
                    validation_report['issues'].append(
                        f"{col}: Values outside expected range [-1, 1]"
                    )

            validation_report['indicators'][col] = indicator_stats

        # Overall quality assessment
        if len(validation_report['issues']) > 0:
            validation_report['overall_quality'] = 'issues_found'

        logger.info(f"Validation completed: {validation_report['overall_quality']}")
        return validation_report

    def save_scalers(self, filepath: str) -> None:
        """
        Save fitted scalers to file for later use.

        Args:
            filepath: Path to save scalers
        """
        import pickle

        if not self.is_fitted:
            logger.warning("Scalers not fitted yet, nothing to save")
            return

        with open(filepath, 'wb') as f:
            pickle.dump(self.scalers, f)

        logger.info(f"Scalers saved to {filepath}")

    def load_scalers(self, filepath: str) -> None:
        """
        Load fitted scalers from file.

        Args:
            filepath: Path to load scalers from
        """
        import pickle

        with open(filepath, 'rb') as f:
            self.scalers = pickle.load(f)

        self.is_fitted = True
        logger.info(f"Scalers loaded from {filepath}")

    def get_summary_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get comprehensive summary statistics for all indicators.

        Args:
            df: DataFrame with calculated indicators

        Returns:
            Dictionary with summary statistics
        """
        feature_cols = self.get_feature_columns(normalized=True)

        summary = {
            'dataset_info': {
                'total_records': len(df),
                'date_range': {
                    'start': df.index.min() if hasattr(df.index, 'min') else 'N/A',
                    'end': df.index.max() if hasattr(df.index, 'max') else 'N/A'
                }
            },
            'indicators_summary': {}
        }

        for col in feature_cols:
            if col in df.columns:
                series = df[col]
                summary['indicators_summary'][col] = {
                    'count': series.notna().sum(),
                    'missing': series.isna().sum(),
                    'mean': series.mean(),
                    'std': series.std(),
                    'min': series.min(),
                    'max': series.max(),
                    'q25': series.quantile(0.25),
                    'q50': series.quantile(0.50),
                    'q75': series.quantile(0.75)
                }

        return summary
