# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: tsl/profiler/protobuf/profile.proto
# Protobuf Python Version: 5.28.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    28,
    3,
    '',
    'tsl/profiler/protobuf/profile.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#tsl/profiler/protobuf/profile.proto\x12\x17tensorflow.tfprof.pprof\"\xf3\x03\n\x07Profile\x12\x37\n\x0bsample_type\x18\x01 \x03(\x0b\x32\".tensorflow.tfprof.pprof.ValueType\x12/\n\x06sample\x18\x02 \x03(\x0b\x32\x1f.tensorflow.tfprof.pprof.Sample\x12\x31\n\x07mapping\x18\x03 \x03(\x0b\x32 .tensorflow.tfprof.pprof.Mapping\x12\x33\n\x08location\x18\x04 \x03(\x0b\x32!.tensorflow.tfprof.pprof.Location\x12\x33\n\x08\x66unction\x18\x05 \x03(\x0b\x32!.tensorflow.tfprof.pprof.Function\x12\x14\n\x0cstring_table\x18\x06 \x03(\t\x12\x13\n\x0b\x64rop_frames\x18\x07 \x01(\x03\x12\x13\n\x0bkeep_frames\x18\x08 \x01(\x03\x12\x12\n\ntime_nanos\x18\t \x01(\x03\x12\x16\n\x0e\x64uration_nanos\x18\n \x01(\x03\x12\x37\n\x0bperiod_type\x18\x0b \x01(\x0b\x32\".tensorflow.tfprof.pprof.ValueType\x12\x0e\n\x06period\x18\x0c \x01(\x03\x12\x0f\n\x07\x63omment\x18\r \x03(\x03\x12\x1b\n\x13\x64\x65\x66\x61ult_sample_type\x18\x0e \x01(\x03\"\'\n\tValueType\x12\x0c\n\x04type\x18\x01 \x01(\x03\x12\x0c\n\x04unit\x18\x02 \x01(\x03\"[\n\x06Sample\x12\x13\n\x0blocation_id\x18\x01 \x03(\x04\x12\r\n\x05value\x18\x02 \x03(\x03\x12-\n\x05label\x18\x03 \x03(\x0b\x32\x1e.tensorflow.tfprof.pprof.Label\".\n\x05Label\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x0b\n\x03str\x18\x02 \x01(\x03\x12\x0b\n\x03num\x18\x03 \x01(\x03\"\xdd\x01\n\x07Mapping\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cmemory_start\x18\x02 \x01(\x04\x12\x14\n\x0cmemory_limit\x18\x03 \x01(\x04\x12\x13\n\x0b\x66ile_offset\x18\x04 \x01(\x04\x12\x10\n\x08\x66ilename\x18\x05 \x01(\x03\x12\x10\n\x08\x62uild_id\x18\x06 \x01(\x03\x12\x15\n\rhas_functions\x18\x07 \x01(\x08\x12\x15\n\rhas_filenames\x18\x08 \x01(\x08\x12\x18\n\x10has_line_numbers\x18\t \x01(\x08\x12\x19\n\x11has_inline_frames\x18\n \x01(\x08\"h\n\x08Location\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nmapping_id\x18\x02 \x01(\x04\x12\x0f\n\x07\x61\x64\x64ress\x18\x03 \x01(\x04\x12+\n\x04line\x18\x04 \x03(\x0b\x32\x1d.tensorflow.tfprof.pprof.Line\")\n\x04Line\x12\x13\n\x0b\x66unction_id\x18\x01 \x01(\x04\x12\x0c\n\x04line\x18\x02 \x01(\x03\"_\n\x08\x46unction\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\x03\x12\x13\n\x0bsystem_name\x18\x03 \x01(\x03\x12\x10\n\x08\x66ilename\x18\x04 \x01(\x03\x12\x12\n\nstart_line\x18\x05 \x01(\x03\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tsl.profiler.protobuf.profile_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PROFILE']._serialized_start=65
  _globals['_PROFILE']._serialized_end=564
  _globals['_VALUETYPE']._serialized_start=566
  _globals['_VALUETYPE']._serialized_end=605
  _globals['_SAMPLE']._serialized_start=607
  _globals['_SAMPLE']._serialized_end=698
  _globals['_LABEL']._serialized_start=700
  _globals['_LABEL']._serialized_end=746
  _globals['_MAPPING']._serialized_start=749
  _globals['_MAPPING']._serialized_end=970
  _globals['_LOCATION']._serialized_start=972
  _globals['_LOCATION']._serialized_end=1076
  _globals['_LINE']._serialized_start=1078
  _globals['_LINE']._serialized_end=1119
  _globals['_FUNCTION']._serialized_start=1121
  _globals['_FUNCTION']._serialized_end=1216
# @@protoc_insertion_point(module_scope)
