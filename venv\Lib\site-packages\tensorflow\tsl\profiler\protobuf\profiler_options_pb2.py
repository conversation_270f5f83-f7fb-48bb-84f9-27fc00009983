# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: tsl/profiler/protobuf/profiler_options.proto
# Protobuf Python Version: 5.28.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    28,
    3,
    '',
    'tsl/profiler/protobuf/profiler_options.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,tsl/profiler/protobuf/profiler_options.proto\x12\ntensorflow\"\xc5\x06\n\x0eProfileOptions\x12\x0f\n\x07version\x18\x05 \x01(\r\x12:\n\x0b\x64\x65vice_type\x18\x06 \x01(\x0e\x32%.tensorflow.ProfileOptions.DeviceType\x12\x1b\n\x13include_dataset_ops\x18\x01 \x01(\x08\x12\x19\n\x11host_tracer_level\x18\x02 \x01(\r\x12\x1b\n\x13\x64\x65vice_tracer_level\x18\x03 \x01(\r\x12\x1b\n\x13python_tracer_level\x18\x04 \x01(\r\x12\x18\n\x10\x65nable_hlo_proto\x18\x07 \x01(\x08\x12\x1a\n\x12start_timestamp_ns\x18\x08 \x01(\x04\x12\x13\n\x0b\x64uration_ms\x18\t \x01(\x04\x12\x17\n\x0frepository_path\x18\n \x01(\t\x12>\n\rtrace_options\x18\x0b \x01(\x0b\x32\'.tensorflow.ProfileOptions.TraceOptions\x12U\n\x16\x61\x64vanced_configuration\x18\x0c \x03(\x0b\x32\x35.tensorflow.ProfileOptions.AdvancedConfigurationEntry\x12$\n\x1craise_error_on_start_failure\x18\r \x01(\x08\x1a\x30\n\x0cTraceOptions\x12 \n\x18host_traceme_filter_mask\x18\x01 \x01(\x04\x1a\x63\n\x13\x41\x64vancedConfigValue\x12\x16\n\x0cstring_value\x18\x01 \x01(\tH\x00\x12\x14\n\nbool_value\x18\x02 \x01(\x08H\x00\x12\x15\n\x0bint64_value\x18\x03 \x01(\x03H\x00\x42\x07\n\x05value\x1al\n\x1a\x41\x64vancedConfigurationEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12=\n\x05value\x18\x02 \x01(\x0b\x32..tensorflow.ProfileOptions.AdvancedConfigValue:\x02\x38\x01\"N\n\nDeviceType\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\x07\n\x03\x43PU\x10\x01\x12\x07\n\x03GPU\x10\x02\x12\x07\n\x03TPU\x10\x03\x12\x14\n\x10PLUGGABLE_DEVICE\x10\x04\"\xd0\x01\n#RemoteProfilerSessionManagerOptions\x12\x34\n\x10profiler_options\x18\x01 \x01(\x0b\x32\x1a.tensorflow.ProfileOptions\x12\x19\n\x11service_addresses\x18\x02 \x03(\t\x12%\n\x1dsession_creation_timestamp_ns\x18\x03 \x01(\x04\x12\x1f\n\x17max_session_duration_ms\x18\x04 \x01(\x04\x12\x10\n\x08\x64\x65lay_ms\x18\x05 \x01(\x04\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tsl.profiler.protobuf.profiler_options_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_PROFILEOPTIONS_ADVANCEDCONFIGURATIONENTRY']._loaded_options = None
  _globals['_PROFILEOPTIONS_ADVANCEDCONFIGURATIONENTRY']._serialized_options = b'8\001'
  _globals['_PROFILEOPTIONS']._serialized_start=61
  _globals['_PROFILEOPTIONS']._serialized_end=898
  _globals['_PROFILEOPTIONS_TRACEOPTIONS']._serialized_start=559
  _globals['_PROFILEOPTIONS_TRACEOPTIONS']._serialized_end=607
  _globals['_PROFILEOPTIONS_ADVANCEDCONFIGVALUE']._serialized_start=609
  _globals['_PROFILEOPTIONS_ADVANCEDCONFIGVALUE']._serialized_end=708
  _globals['_PROFILEOPTIONS_ADVANCEDCONFIGURATIONENTRY']._serialized_start=710
  _globals['_PROFILEOPTIONS_ADVANCEDCONFIGURATIONENTRY']._serialized_end=818
  _globals['_PROFILEOPTIONS_DEVICETYPE']._serialized_start=820
  _globals['_PROFILEOPTIONS_DEVICETYPE']._serialized_end=898
  _globals['_REMOTEPROFILERSESSIONMANAGEROPTIONS']._serialized_start=901
  _globals['_REMOTEPROFILERSESSIONMANAGEROPTIONS']._serialized_end=1109
# @@protoc_insertion_point(module_scope)
